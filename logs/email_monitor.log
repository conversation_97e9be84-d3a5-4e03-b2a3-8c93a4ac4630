2025-07-08 19:06:43,707 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 19:06:43,707 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 19:06:45,073 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 19:06:45,864 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 19:06:45,865 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 19:06:45,865 - services.email_monitor - INFO - Processing email ID: 23
2025-07-08 19:06:46,226 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 19:06:46,228 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_190646_23.txt
2025-07-08 19:06:46,492 - services.email_monitor - INFO - Successfully processed email 23
2025-07-08 19:06:47,021 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:49:32,675 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:49:32,675 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:49:34,803 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:49:35,174 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 21:49:35,870 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:56:47,495 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 21:56:47,495 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:56:47,495 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:56:49,618 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:56:49,904 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 21:56:50,496 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:56:50,497 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 21:57:20,503 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:57:20,506 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:57:21,920 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:57:22,201 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 21:57:22,772 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:57:22,772 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 21:57:52,777 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:57:52,779 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:57:54,140 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:57:54,875 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 21:57:54,875 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 21:57:54,875 - services.email_monitor - INFO - Processing email ID: 24
2025-07-08 21:57:55,200 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 21:57:55,204 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_215755_24.txt
2025-07-08 21:57:55,465 - services.email_monitor - INFO - Successfully processed email 24
2025-07-08 21:57:55,983 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:57:55,984 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 22:00:52,296 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 22:00:52,296 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 22:00:52,296 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 22:00:54,402 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 22:00:55,301 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 22:00:55,301 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 22:00:55,301 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 22:00:55,726 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 22:00:55,727 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_220055_25.txt
2025-07-08 22:00:56,087 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 22:00:56,781 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 22:00:56,781 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 22:31:20,348 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 22:31:20,349 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 22:31:22,103 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 22:31:22,390 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 22:31:22,948 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:25:08,494 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:25:08,495 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:25:08,495 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:25:09,889 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:25:10,222 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 23:25:10,865 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:25:10,866 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:27:20,189 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:27:20,189 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:27:20,189 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:27:21,563 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:27:22,500 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:27:22,500 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 23:27:22,500 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 23:27:22,893 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:27:22,895 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_232722_25.txt
2025-07-08 23:27:22,896 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_232722_25.json
2025-07-08 23:27:22,896 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_232722_25.json
2025-07-08 23:27:22,896 - services.email_monitor - INFO - 🤖 Triggering automated agent workflow...
2025-07-08 23:27:22,897 - services.workflow_orchestrator - INFO - 🚀 Starting O2C workflow workflow_20250708_232722 for file: data/incoming_pos/PO_20250708_232722_25.txt
2025-07-08 23:27:22,897 - services.workflow_orchestrator - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:27:22,941 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:27:23,089 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:27:23,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:27:23,766 - root - ERROR - Error during short_term search: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:27:24,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:27:24,450 - root - ERROR - Error during entities search: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:27:24,487 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:27:25,131 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:27:29,803 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:27:29,803 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:27:29,804 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:27:29,806 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:27:29,836 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:27:30,175 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:28:12,776 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:28:12,778 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:12,779 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:12,782 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:13,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:13,392 - root - ERROR - Error during short_term save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:15,420 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:28:15,947 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:28:35,949 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:28:35,950 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:35,951 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:35,959 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:36,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:36,406 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:36,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:36,804 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:37,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:37,128 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:37,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:37,559 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:38,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:38,273 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:38,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:38,608 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:39,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:39,199 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:39,206 - services.workflow_orchestrator - INFO - Agent returned JSON directly (no file saved)
2025-07-08 23:28:39,206 - services.workflow_orchestrator - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:28:39,206 - services.email_monitor - ERROR - ❌ Automated workflow failed: ['No parsed file generated']
2025-07-08 23:28:39,536 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 23:28:40,143 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:28:40,144 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:29:10,149 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:29:10,151 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:29:11,610 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:29:11,899 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 23:29:12,444 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:29:12,444 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:35:22,730 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:35:22,730 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:35:22,730 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:35:24,505 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:35:25,483 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:35:25,484 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 23:35:25,484 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 23:35:25,829 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:35:25,830 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_233525_25.txt
2025-07-08 23:35:25,830 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_233525_25.json
2025-07-08 23:35:25,830 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_233525_25.json
2025-07-08 23:35:25,831 - services.email_monitor - INFO - 🤖 Triggering automated agent workflow...
2025-07-08 23:35:25,831 - services.workflow_orchestrator - INFO - 🚀 Starting O2C workflow workflow_20250708_233525 for file: data/incoming_pos/PO_20250708_233525_25.txt
2025-07-08 23:35:25,831 - services.workflow_orchestrator - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:35:25,853 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:35:26,204 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:35:30,583 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:35:30,584 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:35:30,584 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:35:30,587 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:35:30,680 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:35:30,992 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:36:04,560 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:36:04,563 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:36:04,563 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:36:04,566 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:36:04,585 - services.workflow_orchestrator - INFO - Agent returned JSON directly (no file saved)
2025-07-08 23:36:04,585 - services.workflow_orchestrator - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:36:04,585 - services.email_monitor - ERROR - ❌ Automated workflow failed: ['No parsed file generated']
2025-07-08 23:36:04,871 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 23:36:06,122 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:36:06,122 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:47:43,389 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:47:43,390 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:47:43,390 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:47:44,829 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:47:45,579 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:47:45,579 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 23:47:45,579 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 23:47:45,877 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:47:45,878 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_234745_25.txt
2025-07-08 23:47:45,878 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_234745_25.json
2025-07-08 23:47:45,878 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_234745_25.json
2025-07-08 23:47:45,878 - services.email_monitor - INFO - 🤖 Triggering automated agent workflow...
2025-07-08 23:47:45,878 - services.workflow_orchestrator - INFO - 🚀 Starting O2C workflow workflow_20250708_234745 for file: data/incoming_pos/PO_20250708_234745_25.txt
2025-07-08 23:47:45,878 - services.workflow_orchestrator - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:47:45,910 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:47:46,397 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:47:50,808 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:47:50,809 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:47:50,809 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:47:50,810 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:47:50,887 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:47:51,187 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:48:35,652 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:48:35,659 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:35,659 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:35,678 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:35,686 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:48:36,089 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:48:42,133 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:48:42,134 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:42,134 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:42,136 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:42,143 - services.workflow_orchestrator - INFO - Successfully created parsed file: PARSED_20250708_234745_25.json
2025-07-08 23:48:42,143 - services.workflow_orchestrator - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:48:42,148 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-08 23:48:42,159 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:48:42,396 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:48:47,315 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:48:47,315 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:47,315 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:47,321 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:47,324 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:48:47,585 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:48:54,392 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:48:54,392 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:54,393 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:54,400 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:48:54,402 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:48:54,662 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:49:07,568 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:49:07,570 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:07,570 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:07,572 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:07,578 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:49:07,880 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:49:41,717 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:49:41,720 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:41,720 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:41,735 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:41,737 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:49:42,041 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:49:50,908 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:49:50,910 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:50,910 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:50,916 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:49:50,920 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:49:51,311 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:23,473 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:50:23,474 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:50:23,475 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:50:23,482 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:50:23,484 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:50:23,980 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:51:03,583 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:51:03,586 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:51:03,586 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:51:03,589 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:51:03,595 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:51:04,123 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:51:47,446 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:51:47,449 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:51:47,449 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:51:47,529 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:51:47,533 - services.email_monitor - WARNING - ⚠️ Workflow completed but validation failed. Workflow ID: workflow_20250708_234745
2025-07-08 23:51:47,836 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 23:51:48,376 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:51:48,376 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:52:18,381 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:52:18,382 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:52:19,840 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:52:20,135 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 23:52:20,670 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:52:20,671 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-09 10:29:07,451 - __main__ - INFO - Starting continuous email monitoring...
2025-07-09 10:29:07,452 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:29:07,452 - __main__ - INFO - Connecting to Gmail IMAP server...
2025-07-09 10:29:08,830 - __main__ - INFO - Successfully connected to Gmail
2025-07-09 10:29:09,662 - __main__ - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-09 10:29:09,662 - __main__ - INFO - Found 1 potential PO emails
2025-07-09 10:29:09,662 - __main__ - INFO - Processing email ID: 25
2025-07-09 10:29:10,011 - __main__ - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-09 10:29:10,012 - __main__ - INFO - Extracted PO content to: data/incoming_pos/PO_20250709_102910_25.txt
2025-07-09 10:29:10,013 - __main__ - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250709_102910_25.json
2025-07-09 10:29:10,013 - __main__ - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250709_102910_25.json
2025-07-09 10:29:10,013 - __main__ - INFO - 📋 Workflow orchestrator not available. Run 'python3 o2c_crew.py' to process manually
2025-07-09 10:29:10,263 - __main__ - INFO - Successfully processed email 25
2025-07-09 10:29:10,759 - __main__ - INFO - Disconnected from Gmail
2025-07-09 10:29:10,759 - __main__ - INFO - Waiting 30 seconds before next check...
2025-07-09 10:31:58,143 - __main__ - INFO - Starting continuous email monitoring...
2025-07-09 10:31:58,144 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:31:58,144 - __main__ - INFO - Connecting to Gmail IMAP server...
2025-07-09 10:31:59,451 - __main__ - INFO - Successfully connected to Gmail
2025-07-09 10:32:00,214 - __main__ - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-09 10:32:00,215 - __main__ - INFO - Found 1 potential PO emails
2025-07-09 10:32:00,215 - __main__ - INFO - Processing email ID: 25
2025-07-09 10:32:00,502 - __main__ - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-09 10:32:00,504 - __main__ - INFO - Extracted PO content to: data/incoming_pos/PO_20250709_103200_25.txt
2025-07-09 10:32:00,505 - __main__ - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250709_103200_25.json
2025-07-09 10:32:00,505 - __main__ - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250709_103200_25.json
2025-07-09 10:32:00,505 - __main__ - INFO - 📋 Workflow orchestrator not available. Run 'python3 o2c_crew.py' to process manually
2025-07-09 10:32:00,762 - __main__ - INFO - Successfully processed email 25
2025-07-09 10:32:01,273 - __main__ - INFO - Disconnected from Gmail
2025-07-09 10:32:01,274 - __main__ - INFO - Waiting 30 seconds before next check...
2025-07-09 10:32:31,276 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:32:31,277 - __main__ - INFO - Connecting to Gmail IMAP server...
2025-07-09 10:32:32,567 - __main__ - INFO - Successfully connected to Gmail
2025-07-09 10:32:32,815 - __main__ - INFO - Found 0 potential PO emails
2025-07-09 10:32:33,305 - __main__ - INFO - Disconnected from Gmail
2025-07-09 10:32:33,306 - __main__ - INFO - Waiting 30 seconds before next check...
2025-07-09 10:33:03,306 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:33:03,306 - __main__ - INFO - Connecting to Gmail IMAP server...
2025-07-09 10:33:04,566 - __main__ - INFO - Successfully connected to Gmail
2025-07-09 10:33:04,853 - __main__ - INFO - Found 0 potential PO emails
2025-07-09 10:33:05,368 - __main__ - INFO - Disconnected from Gmail
2025-07-09 10:33:05,369 - __main__ - INFO - Waiting 30 seconds before next check...
2025-07-09 10:33:35,373 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:33:35,374 - __main__ - INFO - Connecting to Gmail IMAP server...
2025-07-09 10:33:36,733 - __main__ - INFO - Successfully connected to Gmail
2025-07-09 10:33:37,001 - __main__ - INFO - Found 0 potential PO emails
2025-07-09 10:33:37,520 - __main__ - INFO - Disconnected from Gmail
2025-07-09 10:33:37,521 - __main__ - INFO - Waiting 30 seconds before next check...
2025-07-09 10:37:15,213 - __main__ - INFO - Starting continuous email monitoring...
2025-07-09 10:37:15,215 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:37:15,215 - __main__ - INFO - Connecting to Gmail IMAP server...
2025-07-09 10:37:16,436 - __main__ - INFO - Successfully connected to Gmail
2025-07-09 10:37:17,120 - __main__ - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-09 10:37:17,120 - __main__ - INFO - Found 1 potential PO emails
2025-07-09 10:37:17,120 - __main__ - INFO - Processing email ID: 25
2025-07-09 10:37:17,390 - __main__ - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-09 10:37:17,392 - __main__ - INFO - Extracted PO content to: data/incoming_pos/PO_20250709_103717_25.txt
2025-07-09 10:37:17,393 - __main__ - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250709_103717_25.json
2025-07-09 10:37:17,393 - __main__ - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250709_103717_25.json
2025-07-09 10:37:17,393 - __main__ - INFO - 📋 Workflow orchestrator not available. Run 'python3 o2c_crew.py' to process manually
2025-07-09 10:37:17,640 - __main__ - INFO - Successfully processed email 25
2025-07-09 10:37:18,114 - __main__ - INFO - Disconnected from Gmail
2025-07-09 10:37:18,115 - __main__ - INFO - Waiting 30 seconds before next check...
2025-07-09 10:37:48,117 - __main__ - INFO - Starting email monitoring cycle...
2025-07-09 10:37:48,117 - __main__ - INFO - Connecting to Gmail IMAP server...
