#!/usr/bin/env python3
"""
Test Credit Management Agent
Simple test to verify credit management functionality
"""

import json
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_credit_tools():
    """Test credit management tools directly"""
    print("🧪 Testing Credit Management Tools...")
    
    try:
        from agents.tools.credit_management_tools import (
            customer_credit_analyzer,
            payment_history_analyzer,
            ar_aging_analyzer,
            financial_risk_calculator,
            credit_decision_engine,
            credit_limit_optimizer
        )
        
        customer_name = "ABC Manufacturing Corp"
        order_amount = 8811.79
        
        print(f"\n1. 👤 Testing Customer Credit Analyzer...")
        credit_result = customer_credit_analyzer(customer_name, order_amount)
        credit_data = json.loads(credit_result)
        print(f"   Status: {credit_data['status']}")
        if credit_data['status'] == 'SUCCESS':
            analysis = credit_data['credit_analysis']
            print(f"   Credit Score: {analysis['credit_score']}")
            print(f"   Risk Level: {analysis['risk_level']}")
            print(f"   Recommendation: {analysis['recommendation']}")
        
        print(f"\n2. 💳 Testing Payment History Analyzer...")
        payment_result = payment_history_analyzer(customer_name, 12)
        payment_data = json.loads(payment_result)
        print(f"   Status: {payment_data['status']}")
        if payment_data['status'] == 'SUCCESS':
            metrics = payment_data['payment_metrics']
            print(f"   On-time %: {metrics['on_time_percentage']}%")
            print(f"   Avg Days to Pay: {metrics['average_days_to_pay']}")
        
        print(f"\n3. 📊 Testing AR Aging Analyzer...")
        ar_result = ar_aging_analyzer(customer_name)
        ar_data = json.loads(ar_result)
        print(f"   Status: {ar_data['status']}")
        if ar_data['status'] == 'SUCCESS':
            summary = ar_data['ar_summary']
            print(f"   Total Outstanding: ${summary['total_outstanding']:,.2f}")
            print(f"   DSO: {summary['days_sales_outstanding']} days")
            print(f"   Collection Risk: {summary['collection_risk']}")
        
        print(f"\n4. 🎯 Testing Financial Risk Calculator...")
        risk_result = financial_risk_calculator(customer_name, order_amount, "Manufacturing")
        risk_data = json.loads(risk_result)
        print(f"   Status: {risk_data['status']}")
        if risk_data['status'] == 'SUCCESS':
            assessment = risk_data['risk_assessment']
            print(f"   Risk Score: {assessment['overall_risk_score']}")
            print(f"   Risk Level: {assessment['risk_level']}")
        
        print(f"\n5. ⚖️ Testing Credit Decision Engine...")
        decision_result = credit_decision_engine(
            customer_name, order_amount, 785, "MEDIUM", 85, "LOW"
        )
        decision_data = json.loads(decision_result)
        print(f"   Status: {decision_data['status']}")
        if decision_data['status'] == 'SUCCESS':
            decision = decision_data['credit_decision']
            print(f"   Decision: {decision['decision']}")
            print(f"   Risk Level: {decision['risk_level']}")
            terms = decision_data['recommended_terms']
            print(f"   Payment Terms: {terms['payment_terms']}")
        
        print(f"\n6. 💰 Testing Credit Limit Optimizer...")
        limit_result = credit_limit_optimizer(customer_name, 50000, order_amount, 85, "MEDIUM")
        limit_data = json.loads(limit_result)
        print(f"   Status: {limit_data['status']}")
        if limit_data['status'] == 'SUCCESS':
            print(f"   Current Limit: ${limit_data['current_credit_limit']:,.2f}")
            print(f"   Recommended Limit: ${limit_data['recommended_credit_limit']:,.2f}")
            print(f"   Change: ${limit_data['limit_change']:,.2f}")
        
        print(f"\n✅ All Credit Management Tools tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing credit tools: {str(e)}")
        return False

def test_credit_agent():
    """Test the complete credit management agent"""
    print("\n🏦 Testing Complete Credit Management Agent...")
    
    try:
        # Load sample order data
        with open('data/parsed_pos/PARSED_20250708_234745_25.json', 'r') as f:
            order_data = json.load(f)
        
        # Mock validation results
        validation_results = {
            "overall_status": "VALIDATION_FAILED",
            "risk_level": "HIGH",
            "customer_validation": {"status": "PASS"},
            "product_validation": {"status": "FAIL"}
        }
        
        print(f"   Order: {order_data.get('po_number', 'Unknown')}")
        print(f"   Customer: {order_data.get('customer', {}).get('name', 'Unknown')}")
        print(f"   Amount: ${order_data.get('pricing', {}).get('total', 0):,.2f}")
        
        # Import and run credit assessment
        from agents.credit_management_agent import run_credit_assessment
        
        result = run_credit_assessment(order_data, validation_results)
        
        print(f"\n📊 Credit Assessment Result:")
        print(f"   Status: Completed")
        print(f"   Result Length: {len(str(result))} characters")
        
        # Look for key decision indicators in the result
        result_str = str(result)
        if "APPROVE" in result_str:
            print(f"   Decision: APPROVE (found in result)")
        elif "DENY" in result_str or "REJECT" in result_str:
            print(f"   Decision: DENY/REJECT (found in result)")
        elif "MANUAL" in result_str or "REVIEW" in result_str:
            print(f"   Decision: MANUAL REVIEW (found in result)")
        else:
            print(f"   Decision: Unknown")
        
        print(f"\n✅ Credit Management Agent tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing credit agent: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Credit Management Agent Test Suite")
    print("=" * 50)
    
    # Test tools first
    tools_success = test_credit_tools()
    
    # Test complete agent if tools work
    if tools_success:
        agent_success = test_credit_agent()
        
        if agent_success:
            print(f"\n🎉 ALL TESTS PASSED! Credit Management Agent is ready!")
        else:
            print(f"\n⚠️ Tools work but agent has issues")
    else:
        print(f"\n❌ Credit tools have issues - skipping agent test")
