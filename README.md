# O2C Email Monitoring Service

A simple email monitoring service that detects and extracts Purchase Order (PO) content from Gmail emails. This is the first component of the O2C (Order-to-Cash) agentic system.

## Features

- **Gmail IMAP Integration**: Secure connection using app passwords
- **Intelligent PO Detection**: Configurable keywords for subject, sender, and body
- **Content Extraction**: Saves email content as structured text files
- **Continuous Monitoring**: Polls Gmail at configurable intervals
- **Comprehensive Logging**: Detailed logs for monitoring and debugging
- **Error Handling**: Robust error handling and recovery

## Quick Start

### 1. Prerequisites

- Python 3.8 or higher
- Gmail account with 2-Factor Authentication enabled
- Gmail App Password (see setup instructions below)

### 2. Installation

```bash
# Clone or download the project
cd o2c-email-monitor

# Install dependencies
pip install -r requirements.txt

# Create necessary directories
mkdir -p logs data/incoming_pos data/processed_emails config
```

### 3. Gmail Setup

Run the interactive setup script:

```bash
python scripts/setup_gmail.py
```

This will guide you through:
- Generating a Gmail App Password
- Configuring email detection settings
- Testing the connection

#### Manual Gmail App Password Setup

1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Enable 2-Factor Authentication if not already enabled
3. Under "Signing in to Google", select "App passwords"
4. Select "Mail" and "Other (custom name)"
5. Enter "O2C Email Monitor" as the name
6. Copy the 16-character password generated

### 4. Configuration

Edit `config/email_config.json` to customize:

```json
{
  "gmail": {
    "username": "<EMAIL>",
    "app_password": "your-16-character-app-password"
  },
  "po_detection": {
    "subject_keywords": ["PO", "Purchase Order", "P.O."],
    "sender_domains": ["@supplier.com", "@vendor.com"],
    "body_keywords": ["purchase order", "po number"]
  },
  "processing": {
    "polling_interval": 30
  }
}
```

### 5. Running the Monitor

#### Continuous Monitoring
```bash
python services/email_monitor.py
```

#### Single Check
```bash
python -c "from services.email_monitor import EmailMonitor; EmailMonitor().run_monitoring_cycle()"
```

## How It Works

1. **Email Detection**: Monitors Gmail inbox for unread emails
2. **PO Identification**: Uses configurable keywords to identify PO emails
3. **Content Extraction**: Extracts email metadata and body content
4. **File Creation**: Saves content as structured text files in `data/incoming_pos/`
5. **Email Marking**: Marks processed emails as read

## Output Format

Extracted PO files are saved as:
```
data/incoming_pos/PO_20240708_103000_123.txt
```

Content format:
```
=== EMAIL METADATA ===
Email ID: 123
Timestamp: 2024-07-08T10:30:00
Sender: <EMAIL>
Subject: Purchase Order PO-2024-001
Date: Mon, 8 Jul 2024 10:30:00 +0000
Attachments: po_document.pdf

=== EMAIL BODY ===
[Email body content with PO details]
```

## Testing

### Unit Tests
```bash
python tests/test_email_monitor.py --unit
```

### Manual Testing
```bash
python tests/test_email_monitor.py --manual
```

This will show you a sample PO email format to send to your Gmail for testing.

## Configuration Options

### PO Detection Settings

- **subject_keywords**: Keywords to look for in email subjects
- **sender_domains**: Trusted sender domains (include @ symbol)
- **body_keywords**: Keywords to search for in email body
- **polling_interval**: Seconds between email checks (default: 30)

### Logging

Logs are written to:
- `logs/email_monitor.log` (file)
- Console output

Log levels: DEBUG, INFO, WARNING, ERROR

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify 2FA is enabled on Gmail
   - Check app password is correct (16 characters)
   - Ensure "Less secure app access" is NOT enabled

2. **No Emails Detected**
   - Check keyword configuration
   - Verify emails are unread
   - Review logs for detection criteria

3. **Connection Timeout**
   - Check internet connection
   - Verify Gmail IMAP is enabled
   - Try increasing timeout in configuration

### Debug Mode

Enable debug logging by editing the configuration:
```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

## Next Steps

This email monitoring service is designed to integrate with the larger O2C agentic system:

1. **Phase 1**: Email monitoring (current)
2. **Phase 2**: PO parsing with DeepSeek LLM
3. **Phase 3**: Order validation and processing
4. **Phase 4**: Full O2C workflow automation

## Security Notes

- App passwords are stored in plain text in configuration
- Consider using environment variables for production
- Regularly rotate app passwords
- Monitor access logs for suspicious activity

## Support

For issues or questions:
1. Check the logs in `logs/email_monitor.log`
2. Review configuration settings
3. Test with manual email monitoring cycle
4. Verify Gmail app password and settings
