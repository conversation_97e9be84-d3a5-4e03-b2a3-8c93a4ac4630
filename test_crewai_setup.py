#!/usr/bin/env python3
"""
Test script for CrewAI O2C System
Verifies that all components are working correctly
"""

import os
import sys
import json


def test_imports():
    """Test that all required modules can be imported"""
    print("=== Testing Imports ===")
    
    try:
        import crewai
        print("✅ CrewAI imported successfully")
    except ImportError as e:
        print(f"❌ CrewAI import failed: {e}")
        return False
    
    try:
        import langchain
        print("✅ LangChain imported successfully")
    except ImportError as e:
        print(f"❌ LangChain import failed: {e}")
        return False
    
    try:
        from agents.llm.deepseek_llm import create_deepseek_llm
        print("✅ DeepSeek LLM module imported successfully")
    except ImportError as e:
        print(f"❌ DeepSeek LLM import failed: {e}")
        return False
    
    try:
        from agents.tools.po_parsing_tools import get_po_parsing_tools
        print("✅ PO parsing tools imported successfully")
    except ImportError as e:
        print(f"❌ PO parsing tools import failed: {e}")
        return False
    
    return True


def test_directories():
    """Test that required directories exist"""
    print("\n=== Testing Directories ===")
    
    required_dirs = [
        "data/incoming_pos",
        "data/parsed_pos",
        "agents/llm",
        "agents/tools",
        "config"
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory} - NOT FOUND")
            all_exist = False
    
    return all_exist


def test_po_files():
    """Test for available PO files"""
    print("\n=== Testing PO Files ===")
    
    po_folder = "data/incoming_pos"
    if not os.path.exists(po_folder):
        print(f"❌ PO folder {po_folder} not found")
        return False
    
    po_files = [f for f in os.listdir(po_folder) if f.endswith('.txt')]
    
    if po_files:
        print(f"✅ Found {len(po_files)} PO files:")
        for file in po_files:
            print(f"  - {file}")
        return True
    else:
        print("⚠️ No PO files found for testing")
        print("You can test with the extracted PO file from email monitoring")
        return False


def test_deepseek_config():
    """Test DeepSeek configuration"""
    print("\n=== Testing DeepSeek Configuration ===")
    
    # Check environment variable
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if api_key:
        print("✅ DEEPSEEK_API_KEY environment variable is set")
        # Don't print the actual key for security
        print(f"✅ API key length: {len(api_key)} characters")
    else:
        print("❌ DEEPSEEK_API_KEY environment variable not set")
        return False
    
    # Check config file
    config_file = "config/deepseek_config.json"
    if os.path.exists(config_file):
        print(f"✅ Configuration file {config_file} exists")
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            print("✅ Configuration file is valid JSON")
            
            if "deepseek" in config:
                print("✅ DeepSeek configuration section found")
            else:
                print("⚠️ DeepSeek configuration section missing")
                
        except json.JSONDecodeError:
            print("❌ Configuration file is not valid JSON")
            return False
    else:
        print(f"⚠️ Configuration file {config_file} not found")
    
    return True


def test_tools():
    """Test PO parsing tools"""
    print("\n=== Testing PO Parsing Tools ===")
    
    try:
        from agents.tools.po_parsing_tools import get_po_parsing_tools
        
        tools = get_po_parsing_tools()
        print(f"✅ Successfully loaded {len(tools)} tools:")

        tool_names = ["po_file_reader", "po_data_saver", "po_validator", "po_list"]
        for i, tool in enumerate(tools):
            print(f"  - {tool_names[i]}: {tool.__doc__.split('Args:')[0].strip()}")

        # Test the list tool
        list_tool = tools[3]  # po_list function
        result = list_tool()
        print(f"\n📋 PO List Tool Result:")
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tools: {str(e)}")
        return False


def test_agent_creation():
    """Test creating the PO parser agent"""
    print("\n=== Testing Agent Creation ===")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ Cannot test agent creation: DEEPSEEK_API_KEY not set")
        return False
    
    try:
        from agents.po_parser_agent import POParserAgent
        
        print("🔄 Creating PO Parser Agent...")
        agent = POParserAgent(deepseek_api_key=api_key)
        print("✅ PO Parser Agent created successfully")
        
        print(f"✅ Agent role: {agent.agent.role}")
        print(f"✅ Agent tools: {len(agent.tools)} tools available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating agent: {str(e)}")
        return False


def test_crew_creation():
    """Test creating the O2C crew"""
    print("\n=== Testing Crew Creation ===")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ Cannot test crew creation: DEEPSEEK_API_KEY not set")
        return False
    
    try:
        from o2c_crew import O2CCrew
        
        print("🔄 Creating O2C Crew...")
        crew = O2CCrew(deepseek_api_key=api_key)
        print("✅ O2C Crew created successfully")
        
        print(f"✅ Crew agents: {len(crew.crew.agents)} agent(s)")
        print(f"✅ Agent role: {crew.po_parser_agent.role}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating crew: {str(e)}")
        return False


def show_next_steps():
    """Show next steps for using the system"""
    print("\n" + "="*50)
    print("🎯 NEXT STEPS")
    print("="*50)
    
    print("\n1. 📧 Send a test PO email (if you haven't already):")
    print("   - Send email with PO <NAME_EMAIL>")
    print("   - Run: python3 run_email_monitor.py --check")
    
    print("\n2. 🤖 Run the CrewAI PO Parser:")
    print("   - Process latest PO: python3 o2c_crew.py")
    print("   - Process specific file: python3 o2c_crew.py PO_filename.txt")
    print("   - Process all files: python3 o2c_crew.py --all")
    
    print("\n3. 📊 Check the results:")
    print("   - Parsed JSON files: ls -la data/parsed_pos/")
    print("   - View parsed data: cat data/parsed_pos/PARSED_*.json")
    
    print("\n4. 🔧 If you need to setup DeepSeek API:")
    print("   - Run: python3 scripts/setup_crewai.py")


def main():
    """Main test function"""
    print("🧪 CrewAI O2C System Test Suite\n")
    
    tests = [
        ("Imports", test_imports),
        ("Directories", test_directories), 
        ("PO Files", test_po_files),
        ("DeepSeek Config", test_deepseek_config),
        ("Tools", test_tools),
        ("Agent Creation", test_agent_creation),
        ("Crew Creation", test_crew_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} test had issues")
        except Exception as e:
            print(f"❌ {test_name} test failed with error: {str(e)}")
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS")
    print("="*50)
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All tests passed! Your CrewAI O2C system is ready!")
        show_next_steps()
    elif passed >= total - 2:
        print("✅ Most tests passed. System should work with minor issues.")
        show_next_steps()
    else:
        print("⚠️ Several tests failed. Please check the setup.")
        print("\nTry running: python3 scripts/setup_crewai.py")


if __name__ == "__main__":
    main()
