#!/usr/bin/env python3
"""
Gmail Setup Script for O2C Email Monitoring
Helps configure Gmail credentials and test connection
"""

import json
import os
import getpass
import imaplib
from typing import Dict


def load_config() -> Dict:
    """Load existing configuration"""
    config_file = "config/email_config.json"
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Configuration file {config_file} not found!")
        return {}


def save_config(config: Dict):
    """Save configuration to file"""
    config_file = "config/email_config.json"
    os.makedirs("config", exist_ok=True)
    
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Configuration saved to {config_file}")


def setup_gmail_credentials():
    """Interactive setup for Gmail credentials"""
    print("=== Gmail Setup for O2C Email Monitoring ===\n")
    
    print("To use this email monitoring service, you need to:")
    print("1. Enable 2-Factor Authentication on your Gmail account")
    print("2. Generate an App Password for this application")
    print("3. Use the App Password (not your regular password)\n")
    
    print("Steps to generate App Password:")
    print("1. Go to https://myaccount.google.com/security")
    print("2. Under 'Signing in to Google', select 'App passwords'")
    print("3. Select 'Mail' and 'Other (custom name)'")
    print("4. Enter 'O2C Email Monitor' as the name")
    print("5. Copy the 16-character password generated\n")
    
    # Get user input
    email = input("Enter your Gmail address: ").strip()
    
    print("\nEnter your 16-character App Password (format: xxxx xxxx xxxx xxxx):")
    app_password = getpass.getpass("App Password: ").strip().replace(" ", "")
    
    if len(app_password) != 16:
        print("Warning: App password should be 16 characters long")
    
    return email, app_password


def test_gmail_connection(email: str, app_password: str) -> bool:
    """Test Gmail IMAP connection"""
    print("\nTesting Gmail connection...")
    
    try:
        # Create IMAP connection
        mail = imaplib.IMAP4_SSL("imap.gmail.com", 993)
        
        # Login
        mail.login(email, app_password)
        
        # Select inbox
        mail.select('inbox')
        
        # Get inbox info
        status, messages = mail.search(None, 'ALL')
        if status == 'OK':
            total_emails = len(messages[0].split())
            print(f"✓ Connection successful! Found {total_emails} emails in inbox")
        
        # Close connection
        mail.close()
        mail.logout()
        
        return True
        
    except Exception as e:
        print(f"✗ Connection failed: {str(e)}")
        return False


def update_po_detection_settings():
    """Allow user to customize PO detection settings"""
    print("\n=== PO Detection Settings ===")
    print("You can customize how the system detects Purchase Order emails")
    
    customize = input("Do you want to customize PO detection settings? (y/n): ").lower()
    
    if customize != 'y':
        return {}
    
    settings = {}
    
    print("\nSubject Keywords (comma-separated):")
    print("Default: PO, Purchase Order, P.O., order")
    subject_keywords = input("Enter keywords (or press Enter for default): ").strip()
    if subject_keywords:
        settings["subject_keywords"] = [kw.strip() for kw in subject_keywords.split(",")]
    
    print("\nSender Domains (comma-separated, include @ symbol):")
    print("Default: @supplier.com, @vendor.com")
    sender_domains = input("Enter domains (or press Enter for default): ").strip()
    if sender_domains:
        settings["sender_domains"] = [domain.strip() for domain in sender_domains.split(",")]
    
    print("\nBody Keywords (comma-separated):")
    print("Default: purchase order, po number, order number")
    body_keywords = input("Enter keywords (or press Enter for default): ").strip()
    if body_keywords:
        settings["body_keywords"] = [kw.strip() for kw in body_keywords.split(",")]
    
    return settings


def main():
    """Main setup function"""
    print("O2C Email Monitoring Setup\n")
    
    # Load existing configuration
    config = load_config()
    
    # Setup Gmail credentials
    email, app_password = setup_gmail_credentials()
    
    # Test connection
    if not test_gmail_connection(email, app_password):
        print("\nSetup failed. Please check your credentials and try again.")
        return
    
    # Update configuration
    if "gmail" not in config:
        config["gmail"] = {}
    
    config["gmail"]["username"] = email
    config["gmail"]["app_password"] = app_password
    
    # Customize PO detection if desired
    po_settings = update_po_detection_settings()
    if po_settings:
        if "po_detection" not in config:
            config["po_detection"] = {}
        config["po_detection"].update(po_settings)
    
    # Save configuration
    save_config(config)
    
    print("\n✓ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Review the configuration in config/email_config.json")
    print("2. Run the email monitor: python services/email_monitor.py")
    print("3. Send a test PO email to verify detection")


if __name__ == "__main__":
    main()
