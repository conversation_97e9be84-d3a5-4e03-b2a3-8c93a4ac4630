#!/usr/bin/env python3
"""
Live O2C Workflow Demo
Send test emails to demonstrate real-time workflow processing
"""

import smtplib
import time
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_test_po_content(scenario_name: str, customer_data: dict, order_data: dict) -> str:
    """Create test PO content for different scenarios"""
    
    po_content = f"""PURCHASE ORDER - {scenario_name.upper()}

PO Number: {order_data['po_number']}
Date: {order_data['date']}
Vendor: TestCorp Industries

BILL TO:
{customer_data['name']}
{customer_data['address']}
Phone: {customer_data['phone']}
Email: {customer_data['email']}

SHIP TO:
{customer_data['name']} - Warehouse
{customer_data['shipping_address']}
Attention: Receiving Department

VENDOR INFORMATION:
TestCorp Industries
789 Vendor Avenue
Supplier City, TX 75001
Phone: (*************
Email: <EMAIL>

ORDER DETAILS:
Requested Delivery Date: {order_data['delivery_date']}
Payment Terms: {order_data['payment_terms']}
Shipping Method: Standard Ground

LINE ITEMS:"""
    
    total_amount = 0
    for i, item in enumerate(order_data['line_items'], 1):
        line_total = item['quantity'] * item['unit_price']
        total_amount += line_total
        po_content += f"""
{i}. Item Code: {item['item_code']}
   Description: {item['description']}
   Quantity: {item['quantity']} {item['unit_of_measure']}
   Unit Price: ${item['unit_price']:.2f}
   Line Total: ${line_total:.2f}"""
    
    # Calculate pricing
    subtotal = total_amount
    tax_amount = subtotal * 0.0825
    shipping = 125.00
    total = subtotal + tax_amount + shipping
    
    po_content += f"""

PRICING SUMMARY:
Subtotal: ${subtotal:.2f}
Tax (8.25%): ${tax_amount:.2f}
Shipping & Handling: ${shipping:.2f}
TOTAL AMOUNT: ${total:.2f}

SPECIAL INSTRUCTIONS:
- Please deliver to loading dock B between 8:00 AM - 4:00 PM
- All items must be properly packaged and labeled
- Include packing slip with shipment
- Notify receiving department 24 hours before delivery

TERMS AND CONDITIONS:
- All items subject to inspection upon receipt
- Defective items will be returned at vendor expense
- Payment will be made within specified terms
- This purchase order constitutes the entire agreement

Authorized By: {customer_data['authorized_by']}
Title: Procurement Manager
Date: {order_data['date']}

Purchase Order Number: {order_data['po_number']}
Please reference this PO number on all correspondence and invoices.
"""
    
    return po_content

def get_test_scenarios():
    """Define test scenarios for live demo"""
    
    today = datetime.now().strftime("%Y-%m-%d")
    future_date = (datetime.now() + timedelta(days=21)).strftime("%Y-%m-%d")
    
    return {
        "excellent_credit": {
            "customer_data": {
                "name": "Premium Manufacturing Inc",
                "address": "100 Excellence Blvd, Success City, CA 90210",
                "shipping_address": "200 Quality Drive, Success City, CA 90211",
                "phone": "(*************",
                "email": "<EMAIL>",
                "authorized_by": "John Excellence"
            },
            "order_data": {
                "po_number": f"PO-PREMIUM-{datetime.now().strftime('%Y%m%d')}-001",
                "date": today,
                "delivery_date": future_date,
                "payment_terms": "Net 30",
                "line_items": [
                    {
                        "item_code": "WIDGET-A100",
                        "description": "Premium Widget Assembly",
                        "quantity": 50,
                        "unit_of_measure": "EA",
                        "unit_price": 125.50
                    },
                    {
                        "item_code": "BOLT-M8-50",
                        "description": "M8x50mm Stainless Steel Bolt",
                        "quantity": 200,
                        "unit_of_measure": "EA",
                        "unit_price": 2.75
                    }
                ]
            }
        },
        
        "poor_credit": {
            "customer_data": {
                "name": "Struggling Enterprises LLC",
                "address": "500 Difficulty Street, Challenge City, TX 75001",
                "shipping_address": "600 Problem Avenue, Challenge City, TX 75002",
                "phone": "(*************",
                "email": "<EMAIL>",
                "authorized_by": "Bob Struggle"
            },
            "order_data": {
                "po_number": f"PO-STRUGGLE-{datetime.now().strftime('%Y%m%d')}-001",
                "date": today,
                "delivery_date": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"),  # Rush order
                "payment_terms": "Net 30",
                "line_items": [
                    {
                        "item_code": "WIDGET-B200",
                        "description": "Standard Widget",
                        "quantity": 25,
                        "unit_of_measure": "EA",
                        "unit_price": 85.00
                    },
                    {
                        "item_code": "CABLE-USB-3M",
                        "description": "USB Cable 3 Meter",
                        "quantity": 100,
                        "unit_of_measure": "EA",
                        "unit_price": 15.50
                    }
                ]
            }
        },
        
        "new_customer": {
            "customer_data": {
                "name": "Startup Innovations Corp",
                "address": "300 Innovation Drive, Tech City, CA 94000",
                "shipping_address": "400 Startup Lane, Tech City, CA 94001",
                "phone": "(*************",
                "email": "<EMAIL>",
                "authorized_by": "Alice Innovator"
            },
            "order_data": {
                "po_number": f"PO-STARTUP-{datetime.now().strftime('%Y%m%d')}-001",
                "date": today,
                "delivery_date": future_date,
                "payment_terms": "Net 30",
                "line_items": [
                    {
                        "item_code": "WIDGET-A100",
                        "description": "Premium Widget Assembly",
                        "quantity": 20,
                        "unit_of_measure": "EA",
                        "unit_price": 125.50
                    }
                ]
            }
        }
    }

def send_test_email(scenario_name: str, po_content: str):
    """Send test email with PO content"""
    
    # Email configuration (using your Gmail credentials)
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    sender_email = "<EMAIL>"
    sender_password = "cmkd sqdx prqn yorm"  # App password
    recipient_email = "<EMAIL>"
    
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = f"Test PO - {scenario_name.replace('_', ' ').title()} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Email body
        body = f"""Dear TestCorp Industries,

Please find attached our purchase order for immediate processing.

This is a test scenario: {scenario_name.replace('_', ' ').title()}

Please confirm receipt and provide estimated delivery schedule.

Best regards,
Procurement Team

---
PURCHASE ORDER DETAILS:
{po_content}
"""
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        text = msg.as_string()
        server.sendmail(sender_email, recipient_email, text)
        server.quit()
        
        print(f"✅ Test email sent successfully for scenario: {scenario_name}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to send test email for {scenario_name}: {str(e)}")
        return False

def run_live_demo():
    """Run live workflow demonstration"""
    
    print("🚀 LIVE O2C WORKFLOW DEMONSTRATION")
    print("=" * 60)
    print("Sending test emails to trigger real-time workflow processing...")
    print("=" * 60)
    
    scenarios = get_test_scenarios()
    
    for i, (scenario_name, scenario_data) in enumerate(scenarios.items(), 1):
        print(f"\n📧 Test {i}/3: {scenario_name.replace('_', ' ').title()}")
        print("-" * 40)
        
        # Create PO content
        po_content = create_test_po_content(
            scenario_name,
            scenario_data["customer_data"],
            scenario_data["order_data"]
        )
        
        # Send test email
        success = send_test_email(scenario_name, po_content)
        
        if success:
            print(f"   Customer: {scenario_data['customer_data']['name']}")
            print(f"   PO Number: {scenario_data['order_data']['po_number']}")
            print(f"   Expected Outcome: {get_expected_outcome(scenario_name)}")
            print(f"   ⏱️ Email sent - workflow should trigger automatically")
        
        # Wait between emails to avoid spam detection
        if i < len(scenarios):
            print(f"   ⏳ Waiting 30 seconds before next test...")
            time.sleep(30)
    
    print(f"\n🎯 LIVE DEMO COMPLETE!")
    print(f"   All test emails sent successfully")
    print(f"   Monitor your email monitoring service to see real-time processing")
    print(f"   Each scenario will demonstrate different decision outcomes")

def get_expected_outcome(scenario_name: str) -> str:
    """Get expected outcome for each scenario"""
    outcomes = {
        "excellent_credit": "✅ APPROVE - Immediate processing",
        "poor_credit": "👥 MANUAL_REVIEW - Credit manager escalation",
        "new_customer": "⚠️ CONDITIONAL - Credit application required"
    }
    return outcomes.get(scenario_name, "❓ Unknown")

if __name__ == "__main__":
    print("🧪 Live O2C Workflow Demo")
    print("This will send test emails to trigger your automated workflow")
    
    response = input("\nProceed with live demo? (y/n): ")
    if response.lower() == 'y':
        run_live_demo()
    else:
        print("Demo cancelled.")
