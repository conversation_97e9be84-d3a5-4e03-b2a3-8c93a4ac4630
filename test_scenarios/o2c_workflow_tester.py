#!/usr/bin/env python3
"""
O2C Workflow Testing Suite
Comprehensive testing of the complete Order-to-Cash workflow with various customer scenarios
"""

import os
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class O2CWorkflowTester:
    """Comprehensive O2C workflow testing framework"""
    
    def __init__(self):
        self.test_results = []
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """Setup test environment and directories"""
        os.makedirs("test_scenarios/test_data", exist_ok=True)
        os.makedirs("test_scenarios/results", exist_ok=True)
        print("🧪 O2C Workflow Testing Suite Initialized")
        print("=" * 60)
    
    def create_test_po(self, scenario_name: str, customer_data: Dict, order_data: Dict) -> str:
        """Create a test PO file for a scenario"""
        po_content = f"""PURCHASE ORDER

PO Number: {order_data['po_number']}
Date: {order_data['date']}
Vendor: {order_data['vendor']}

BILL TO:
{customer_data['name']}
{customer_data['billing_address']['street']}
{customer_data['billing_address']['city']}, {customer_data['billing_address']['state']} {customer_data['billing_address']['zip']}
Phone: {customer_data['contact']['phone']}
Email: {customer_data['contact']['email']}

SHIP TO:
{customer_data['name']} - Warehouse
{customer_data['shipping_address']['street']}
{customer_data['shipping_address']['city']}, {customer_data['shipping_address']['state']} {customer_data['shipping_address']['zip']}
Attention: Receiving Department

VENDOR INFORMATION:
{order_data['vendor']}
789 Vendor Avenue
Supplier City, TX 75001
Phone: (*************
Email: <EMAIL>

ORDER DETAILS:
Requested Delivery Date: {order_data['delivery_date']}
Payment Terms: {order_data['payment_terms']}
Shipping Method: Standard Ground

LINE ITEMS:"""
        
        total_amount = 0
        for i, item in enumerate(order_data['line_items'], 1):
            line_total = item['quantity'] * item['unit_price']
            total_amount += line_total
            po_content += f"""
{i}. Item Code: {item['item_code']}
   Description: {item['description']}
   Quantity: {item['quantity']} {item['unit_of_measure']}
   Unit Price: ${item['unit_price']:.2f}
   Line Total: ${line_total:.2f}"""
        
        # Calculate pricing
        subtotal = total_amount
        tax_rate = order_data.get('tax_rate', 8.25)
        tax_amount = subtotal * (tax_rate / 100)
        shipping = order_data.get('shipping', 125.00)
        total = subtotal + tax_amount + shipping
        
        po_content += f"""

PRICING SUMMARY:
Subtotal: ${subtotal:.2f}
Tax ({tax_rate}%): ${tax_amount:.2f}
Shipping & Handling: ${shipping:.2f}
TOTAL AMOUNT: ${total:.2f}

SPECIAL INSTRUCTIONS:
- Please deliver to loading dock B between 8:00 AM - 4:00 PM
- All items must be properly packaged and labeled
- Include packing slip with shipment
- Notify receiving department 24 hours before delivery

TERMS AND CONDITIONS:
- All items subject to inspection upon receipt
- Defective items will be returned at vendor expense
- Payment will be made within 30 days of receipt and approval
- This purchase order constitutes the entire agreement

Authorized By: {customer_data['authorized_by']}
Title: Procurement Manager
Date: {order_data['date']}

Purchase Order Number: {order_data['po_number']}
Please reference this PO number on all correspondence and invoices.
"""
        
        # Save test PO file
        filename = f"test_scenarios/test_data/{scenario_name}_PO.txt"
        with open(filename, 'w') as f:
            f.write(po_content)
        
        return filename
    
    def simulate_workflow_stages(self, scenario_name: str, customer_data: Dict, order_data: Dict) -> Dict[str, Any]:
        """Simulate the complete O2C workflow for a scenario"""
        print(f"\n🔄 Testing Scenario: {scenario_name}")
        print("-" * 50)
        
        workflow_result = {
            "scenario": scenario_name,
            "started_at": datetime.now().isoformat(),
            "stages": {},
            "overall_status": "IN_PROGRESS"
        }
        
        try:
            # Stage 1: Email Processing (Simulated)
            print("📧 Stage 1: Email Processing...")
            workflow_result["stages"]["email_processing"] = {
                "status": "SUCCESS",
                "message": f"Email detected and PO extracted for {customer_data['name']}"
            }
            
            # Stage 2: PO Parsing (Simulated)
            print("🧠 Stage 2: AI-Powered PO Parsing...")
            parsed_data = self.simulate_po_parsing(customer_data, order_data)
            workflow_result["stages"]["po_parsing"] = {
                "status": "SUCCESS",
                "parsed_data": parsed_data,
                "message": "PO successfully parsed and structured"
            }
            
            # Stage 3: Order Validation (Simulated)
            print("✅ Stage 3: Order Validation...")
            validation_result = self.simulate_order_validation(parsed_data)
            workflow_result["stages"]["order_validation"] = validation_result
            
            # Stage 4: Credit Assessment (Simulated)
            print("🏦 Stage 4: Credit Management & Risk Assessment...")
            credit_result = self.simulate_credit_assessment(customer_data, order_data, validation_result)
            workflow_result["stages"]["credit_assessment"] = credit_result
            
            # Determine final status
            credit_decision = credit_result.get("decision", "UNKNOWN")
            if credit_decision == "APPROVE":
                workflow_result["overall_status"] = "APPROVED"
            elif credit_decision == "APPROVE_WITH_CONDITIONS":
                workflow_result["overall_status"] = "APPROVED_WITH_CONDITIONS"
            elif credit_decision == "MANUAL_REVIEW":
                workflow_result["overall_status"] = "REQUIRES_MANUAL_REVIEW"
            else:
                workflow_result["overall_status"] = "DENIED"
            
            workflow_result["completed_at"] = datetime.now().isoformat()
            
            # Print results
            self.print_scenario_results(workflow_result)
            
            return workflow_result
            
        except Exception as e:
            workflow_result["overall_status"] = "ERROR"
            workflow_result["error"] = str(e)
            print(f"❌ Error in workflow: {str(e)}")
            return workflow_result
    
    def simulate_po_parsing(self, customer_data: Dict, order_data: Dict) -> Dict[str, Any]:
        """Simulate PO parsing stage"""
        return {
            "po_number": order_data["po_number"],
            "date": order_data["date"],
            "customer": customer_data,
            "line_items": order_data["line_items"],
            "pricing": {
                "subtotal": sum(item["quantity"] * item["unit_price"] for item in order_data["line_items"]),
                "tax_rate": order_data.get("tax_rate", 8.25),
                "tax_amount": sum(item["quantity"] * item["unit_price"] for item in order_data["line_items"]) * 0.0825,
                "shipping": order_data.get("shipping", 125.00),
                "total": sum(item["quantity"] * item["unit_price"] for item in order_data["line_items"]) * 1.0825 + 125.00
            },
            "delivery": {
                "address": f"{customer_data['shipping_address']['street']}, {customer_data['shipping_address']['city']}, {customer_data['shipping_address']['state']} {customer_data['shipping_address']['zip']}",
                "requested_date": order_data["delivery_date"],
                "shipping_method": "Standard Ground"
            }
        }
    
    def simulate_order_validation(self, parsed_data: Dict) -> Dict[str, Any]:
        """Simulate order validation stage"""
        validation_issues = []
        validation_warnings = []
        
        # Check for common validation issues
        total_amount = parsed_data["pricing"]["total"]
        if total_amount > 100000:
            validation_issues.append("Order amount exceeds standard limit")
        
        # Check delivery date
        delivery_date = datetime.strptime(parsed_data["delivery"]["requested_date"], "%Y-%m-%d")
        if delivery_date < datetime.now():
            validation_issues.append("Delivery date is in the past")
        elif delivery_date < datetime.now() + timedelta(days=14):
            validation_warnings.append("Short delivery lead time")
        
        # Determine validation status
        if validation_issues:
            status = "FAIL"
        elif validation_warnings:
            status = "REVIEW"
        else:
            status = "PASS"
        
        return {
            "status": status,
            "issues": validation_issues,
            "warnings": validation_warnings,
            "message": f"Validation {status}: {len(validation_issues)} issues, {len(validation_warnings)} warnings"
        }
    
    def print_scenario_results(self, workflow_result: Dict[str, Any]):
        """Print formatted results for a scenario"""
        scenario = workflow_result["scenario"]
        status = workflow_result["overall_status"]
        
        print(f"\n📊 SCENARIO RESULTS: {scenario}")
        print("=" * 50)
        
        # Print stage results
        for stage_name, stage_result in workflow_result["stages"].items():
            stage_status = stage_result.get("status", "UNKNOWN")
            stage_message = stage_result.get("message", "No details")
            
            if stage_status == "SUCCESS":
                print(f"✅ {stage_name.replace('_', ' ').title()}: {stage_message}")
            elif stage_status == "FAIL":
                print(f"❌ {stage_name.replace('_', ' ').title()}: {stage_message}")
            else:
                print(f"⚠️ {stage_name.replace('_', ' ').title()}: {stage_message}")
        
        # Print final decision
        print(f"\n🎯 FINAL DECISION: {status}")
        
        if status == "APPROVED":
            print("✅ ORDER APPROVED - Process immediately with standard terms")
        elif status == "APPROVED_WITH_CONDITIONS":
            print("⚠️ ORDER APPROVED WITH CONDITIONS - Apply special terms and monitoring")
        elif status == "REQUIRES_MANUAL_REVIEW":
            print("👥 MANUAL REVIEW REQUIRED - Escalate to credit manager")
        elif status == "DENIED":
            print("❌ ORDER DENIED - Offer alternative payment terms")
        else:
            print("❓ UNKNOWN STATUS - System error occurred")
        
        # Credit assessment details
        if "credit_assessment" in workflow_result["stages"]:
            credit_info = workflow_result["stages"]["credit_assessment"]
            if "risk_score" in credit_info:
                print(f"   Risk Score: {credit_info['risk_score']}")
            if "risk_level" in credit_info:
                print(f"   Risk Level: {credit_info['risk_level']}")
            if "payment_terms" in credit_info:
                print(f"   Payment Terms: {credit_info['payment_terms']}")
        
        print("-" * 50)

def get_test_scenarios():
    """Define comprehensive test scenarios"""
    return {
        "excellent_credit": {
            "description": "High-quality customer with excellent credit and payment history",
            "customer_data": {
                "name": "Premium Manufacturing Inc",
                "billing_address": {
                    "street": "100 Excellence Blvd",
                    "city": "Success City",
                    "state": "CA",
                    "zip": "90210"
                },
                "shipping_address": {
                    "street": "200 Quality Drive",
                    "city": "Success City", 
                    "state": "CA",
                    "zip": "90211"
                },
                "contact": {
                    "phone": "(*************",
                    "email": "<EMAIL>"
                },
                "authorized_by": "John Excellence",
                "credit_profile": {
                    "credit_score": 850,
                    "payment_history": "Excellent",
                    "financial_strength": "Very Strong"
                }
            }
        }
    }

if __name__ == "__main__":
    tester = O2CWorkflowTester()
    scenarios = get_test_scenarios()
    
    print("🧪 Starting O2C Workflow Testing...")
    
    for scenario_name, scenario_data in scenarios.items():
        # This would run the actual test
        print(f"Testing {scenario_name}...")
