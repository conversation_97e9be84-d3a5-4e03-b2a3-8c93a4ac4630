#!/usr/bin/env python3
"""
Test script for Email Monitoring Service
Tests email detection and content extraction functionality
"""

import os
import sys
import json
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path to import email_monitor
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.email_monitor import EmailMonitor


class TestEmailMonitor(unittest.TestCase):
    """Test cases for EmailMonitor class"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary config file
        self.temp_config = {
            "gmail": {
                "username": "<EMAIL>",
                "app_password": "testpassword1234",
                "imap_server": "imap.gmail.com",
                "imap_port": 993
            },
            "po_detection": {
                "subject_keywords": ["PO", "Purchase Order"],
                "sender_domains": ["@supplier.com"],
                "body_keywords": ["purchase order", "po number"]
            },
            "processing": {
                "output_folder": "test_output",
                "processed_folder": "test_processed",
                "polling_interval": 5
            }
        }
        
        # Create temporary config file
        self.config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.temp_config, self.config_file)
        self.config_file.close()
        
        # Initialize EmailMonitor with test config
        self.monitor = EmailMonitor(self.config_file.name)
    
    def tearDown(self):
        """Clean up test environment"""
        # Remove temporary config file
        os.unlink(self.config_file.name)
        
        # Clean up test directories
        import shutil
        for folder in ["test_output", "test_processed", "logs"]:
            if os.path.exists(folder):
                shutil.rmtree(folder)
    
    def test_config_loading(self):
        """Test configuration loading"""
        self.assertEqual(self.monitor.config["gmail"]["username"], "<EMAIL>")
        self.assertEqual(self.monitor.config["gmail"]["app_password"], "testpassword1234")
        self.assertIn("PO", self.monitor.config["po_detection"]["subject_keywords"])
    
    def test_default_config_fallback(self):
        """Test fallback to default config when file doesn't exist"""
        monitor = EmailMonitor("nonexistent_config.json")
        self.assertEqual(monitor.config["gmail"]["username"], "")
        self.assertIn("PO", monitor.config["po_detection"]["subject_keywords"])
    
    @patch('imaplib.IMAP4_SSL')
    def test_gmail_connection(self, mock_imap):
        """Test Gmail IMAP connection"""
        # Mock successful connection
        mock_connection = Mock()
        mock_imap.return_value = mock_connection
        mock_connection.login.return_value = None
        mock_connection.select.return_value = ('OK', [])
        
        result = self.monitor.connect_to_gmail()
        
        self.assertTrue(result)
        mock_imap.assert_called_once_with("imap.gmail.com", 993)
        mock_connection.login.assert_called_once_with("<EMAIL>", "testpassword1234")
        mock_connection.select.assert_called_once_with('inbox')
    
    @patch('imaplib.IMAP4_SSL')
    def test_gmail_connection_failure(self, mock_imap):
        """Test Gmail connection failure handling"""
        # Mock connection failure
        mock_imap.side_effect = Exception("Connection failed")
        
        result = self.monitor.connect_to_gmail()
        
        self.assertFalse(result)
    
    def test_email_body_extraction(self):
        """Test email body text extraction"""
        # Create mock email message
        mock_email = Mock()
        mock_email.is_multipart.return_value = False
        mock_email.get_payload.return_value = b"This is a test email body"
        
        body = self.monitor._extract_email_body(mock_email)
        
        self.assertEqual(body, "This is a test email body")
    
    def test_po_content_saving(self):
        """Test PO content saving to file"""
        # Create test output directory
        os.makedirs("test_output", exist_ok=True)
        
        test_po_data = {
            "email_id": "123",
            "timestamp": "2024-07-08T10:30:00",
            "sender": "<EMAIL>",
            "subject": "Purchase Order PO-2024-001",
            "date": "Mon, 8 Jul 2024 10:30:00 +0000",
            "body": "This is a test purchase order with PO number PO-2024-001",
            "attachments": ["po_document.pdf"],
            "text_attachments": {"po_details.txt": "PO Number: PO-2024-001\nTotal: $1000.00"}
        }
        
        filepath = self.monitor._save_po_content(test_po_data)
        
        self.assertIsNotNone(filepath)
        self.assertTrue(os.path.exists(filepath))
        
        # Verify file content
        with open(filepath, 'r') as f:
            content = f.read()
            self.assertIn("Email ID: 123", content)
            self.assertIn("Purchase Order PO-2024-001", content)
            self.assertIn("po_document.pdf", content)
            self.assertIn("This is a test purchase order", content)
            self.assertIn("TEXT ATTACHMENTS", content)
            self.assertIn("po_details.txt", content)
            self.assertIn("PO Number: PO-2024-001", content)


def create_sample_po_email():
    """Create a sample PO email for manual testing"""
    sample_email = """
Subject: Purchase Order PO-2024-001
From: <EMAIL>
To: <EMAIL>
Date: Mon, 8 Jul 2024 10:30:00 +0000

Dear Vendor,

Please find our purchase order details below:

Purchase Order Number: PO-2024-001
Order Date: July 8, 2024
Delivery Date: July 15, 2024

Ship To:
Your Company Inc.
123 Main Street
Anytown, CA 12345

Bill To:
Your Company Inc.
Accounts Payable Department
123 Main Street
Anytown, CA 12345

Items Ordered:
1. Product Code: PROD-001
   Description: Widget Type A
   Quantity: 100
   Unit Price: $25.00
   Total: $2,500.00

2. Product Code: PROD-002
   Description: Widget Type B
   Quantity: 50
   Unit Price: $35.00
   Total: $1,750.00

Subtotal: $4,250.00
Tax (8.25%): $350.63
Total Amount: $4,600.63

Payment Terms: Net 30
Special Instructions: Please deliver to loading dock B

Thank you for your prompt attention to this order.

Best regards,
John Doe
Procurement Manager
<EMAIL>
555-123-4567
"""
    
    return sample_email


def run_manual_test():
    """Run manual test with sample email"""
    print("=== Manual Email Monitor Test ===\n")
    
    # Check if configuration exists
    if not os.path.exists("config/email_config.json"):
        print("Configuration file not found. Please run setup_gmail.py first.")
        return
    
    # Create sample PO email content
    sample_email = create_sample_po_email()
    
    print("Sample PO Email Content:")
    print("-" * 50)
    print(sample_email)
    print("-" * 50)
    
    print("\nTo test the email monitor:")
    print("1. Send an email with the above content to your Gmail account")
    print("2. Run: python services/email_monitor.py")
    print("3. Check the data/incoming_pos folder for extracted PO files")
    
    print("\nOr run a single monitoring cycle:")
    print("python -c \"from services.email_monitor import EmailMonitor; EmailMonitor().run_monitoring_cycle()\"")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Email Monitor")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--manual", action="store_true", help="Run manual test")
    
    args = parser.parse_args()
    
    if args.unit:
        unittest.main(argv=[''])
    elif args.manual:
        run_manual_test()
    else:
        print("Usage:")
        print("  python test_email_monitor.py --unit    # Run unit tests")
        print("  python test_email_monitor.py --manual  # Run manual test")
