#!/usr/bin/env python3
"""
Email Monitoring Service for O2C System
Monitors Gmail for new PO emails and extracts content for processing
"""

import imaplib
import email
import os
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
import re

# Import workflow orchestrator for automated agent triggering
try:
    import sys
    from pathlib import Path

    # Ensure project root is in Python path
    project_root = Path(__file__).parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    from services.workflow_orchestrator import trigger_automated_workflow
    WORKFLOW_INTEGRATION_ENABLED = True
    print("✅ Workflow orchestrator successfully loaded - automated processing enabled")
except ImportError as e:
    WORKFLOW_INTEGRATION_ENABLED = False
    print(f"⚠️ Workflow orchestrator not available - running in manual mode. Error: {e}")
except Exception as e:
    WORKFLOW_INTEGRATION_ENABLED = False
    print(f"⚠️ Workflow orchestrator error - running in manual mode. Error: {e}")


class EmailMonitor:
    """Simple email monitoring service for PO detection and extraction"""
    
    def __init__(self, config_file: str = "config/email_config.json"):
        """Initialize email monitor with configuration"""
        self.config = self._load_config(config_file)
        self.logger = self._setup_logging()
        self.mail_connection = None
        
    def _load_config(self, config_file: str) -> Dict:
        """Load email configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default configuration if file doesn't exist
            return {
                "gmail": {
                    "username": "",
                    "app_password": "",
                    "imap_server": "imap.gmail.com",
                    "imap_port": 993
                },
                "po_detection": {
                    "subject_keywords": ["PO", "Purchase Order", "P.O.", "order"],
                    "sender_domains": ["@supplier.com", "@vendor.com"],
                    "body_keywords": ["purchase order", "po number", "order number"]
                },
                "processing": {
                    "output_folder": "data/incoming_pos",
                    "processed_folder": "data/processed_emails",
                    "polling_interval": 30
                }
            }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/email_monitor.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def connect_to_gmail(self) -> bool:
        """Establish connection to Gmail IMAP server"""
        try:
            self.logger.info("Connecting to Gmail IMAP server...")
            
            # Create IMAP connection
            self.mail_connection = imaplib.IMAP4_SSL(
                self.config["gmail"]["imap_server"],
                self.config["gmail"]["imap_port"]
            )
            
            # Login with app password
            self.mail_connection.login(
                self.config["gmail"]["username"],
                self.config["gmail"]["app_password"]
            )
            
            # Select inbox
            self.mail_connection.select('inbox')
            
            self.logger.info("Successfully connected to Gmail")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Gmail: {str(e)}")
            return False
    
    def disconnect(self):
        """Close Gmail connection"""
        if self.mail_connection:
            try:
                self.mail_connection.close()
                self.mail_connection.logout()
                self.logger.info("Disconnected from Gmail")
            except Exception as e:
                self.logger.error(f"Error disconnecting: {str(e)}")
    
    def search_po_emails(self) -> List[str]:
        """Search for emails that might contain purchase orders"""
        try:
            # Search for unread emails
            status, messages = self.mail_connection.search(None, 'UNSEEN')
            
            if status != 'OK':
                self.logger.error("Failed to search emails")
                return []
            
            email_ids = messages[0].split()
            po_email_ids = []
            
            for email_id in email_ids:
                if self._is_po_email(email_id):
                    po_email_ids.append(email_id.decode())
            
            self.logger.info(f"Found {len(po_email_ids)} potential PO emails")
            return po_email_ids
            
        except Exception as e:
            self.logger.error(f"Error searching emails: {str(e)}")
            return []
    
    def _is_po_email(self, email_id: bytes) -> bool:
        """Check if email contains PO indicators"""
        try:
            # Fetch email headers and body
            status, msg_data = self.mail_connection.fetch(email_id, '(RFC822)')
            
            if status != 'OK':
                return False
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Check subject for PO keywords
            subject = email_message.get('Subject', '').lower()
            subject_match = any(keyword.lower() in subject 
                              for keyword in self.config["po_detection"]["subject_keywords"])
            
            # Check sender domain
            sender = email_message.get('From', '').lower()
            sender_match = any(domain.lower() in sender 
                             for domain in self.config["po_detection"]["sender_domains"])
            
            # Check body for PO keywords (basic check)
            body_text = self._extract_email_body(email_message).lower()
            body_match = any(keyword.lower() in body_text
                           for keyword in self.config["po_detection"]["body_keywords"])

            # Check text attachments for PO keywords
            text_attachments = self._extract_text_attachments(email_message)
            attachment_match = False
            if text_attachments:
                for filename, content in text_attachments.items():
                    content_lower = content.lower()
                    if any(keyword.lower() in content_lower
                          for keyword in self.config["po_detection"]["body_keywords"]):
                        attachment_match = True
                        break

            # Return True if any criteria match
            return subject_match or sender_match or body_match or attachment_match
            
        except Exception as e:
            self.logger.error(f"Error checking email {email_id}: {str(e)}")
            return False
    
    def _extract_email_body(self, email_message) -> str:
        """Extract text content from email"""
        body = ""

        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    body += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')

        return body

    def _extract_text_attachments(self, email_message) -> Dict[str, str]:
        """Extract content from text attachments"""
        attachments = {}

        if email_message.is_multipart():
            for part in email_message.walk():
                # Check if this part is an attachment
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    if filename:
                        # Check if it's a text file
                        content_type = part.get_content_type()
                        if (content_type == "text/plain" or
                            filename.lower().endswith(('.txt', '.text', '.po'))):
                            try:
                                # Extract attachment content
                                attachment_content = part.get_payload(decode=True)
                                if attachment_content:
                                    text_content = attachment_content.decode('utf-8', errors='ignore')
                                    attachments[filename] = text_content
                                    self.logger.info(f"Extracted text attachment: {filename}")
                            except Exception as e:
                                self.logger.error(f"Error extracting attachment {filename}: {str(e)}")

        return attachments
    
    def extract_po_content(self, email_id: str) -> Optional[Dict]:
        """Extract PO content from email and save as text file"""
        try:
            # Fetch full email
            status, msg_data = self.mail_connection.fetch(email_id, '(RFC822)')
            
            if status != 'OK':
                return None
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract metadata
            po_data = {
                "email_id": email_id,
                "timestamp": datetime.now().isoformat(),
                "sender": email_message.get('From', ''),
                "subject": email_message.get('Subject', ''),
                "date": email_message.get('Date', ''),
                "body": self._extract_email_body(email_message),
                "attachments": [],
                "text_attachments": {}
            }

            # Extract text attachments content
            text_attachments = self._extract_text_attachments(email_message)
            po_data["text_attachments"] = text_attachments

            # Extract all attachments list
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if filename:
                            po_data["attachments"].append(filename)
            
            # Save PO content to file
            output_file = self._save_po_content(po_data)

            if output_file:
                self.logger.info(f"Extracted PO content to: {output_file}")

                # Save processed email metadata
                metadata_file = self._save_processed_email_metadata(po_data, output_file)
                if metadata_file:
                    self.logger.info(f"Saved email metadata to: {metadata_file}")

                # 🚀 AUTOMATED WORKFLOW TRIGGER
                automated_workflow_enabled = self.config.get("processing", {}).get("automated_workflow", False)

                if WORKFLOW_INTEGRATION_ENABLED and automated_workflow_enabled:
                    self.logger.info("🤖 Triggering automated agent workflow...")
                    try:
                        workflow_result = trigger_automated_workflow(output_file)
                        status = workflow_result["overall_status"]
                        workflow_id = workflow_result['workflow_id']

                        if status == "APPROVED":
                            self.logger.info(f"✅ Order APPROVED! Workflow ID: {workflow_id}")
                        elif status == "APPROVED_WITH_CONDITIONS":
                            self.logger.info(f"⚠️ Order APPROVED WITH CONDITIONS! Workflow ID: {workflow_id}")
                        elif status == "REQUIRES_MANUAL_REVIEW":
                            self.logger.warning(f"👥 Order requires MANUAL REVIEW! Workflow ID: {workflow_id}")
                        elif status == "DENIED":
                            self.logger.warning(f"❌ Order DENIED! Workflow ID: {workflow_id}")
                        elif status == "VALIDATION_FAILED":
                            self.logger.warning(f"⚠️ Workflow completed but validation failed. Workflow ID: {workflow_id}")
                        else:
                            self.logger.error(f"❌ Automated workflow failed: {workflow_result.get('errors', 'Unknown error')}")
                    except Exception as e:
                        self.logger.error(f"❌ Failed to trigger automated workflow: {str(e)}")
                elif not automated_workflow_enabled:
                    self.logger.info("📋 Automated workflow disabled in config. Run 'python3 o2c_crew.py' to process manually")
                else:
                    self.logger.info("📋 Workflow orchestrator not available. Run 'python3 o2c_crew.py' to process manually")

                return po_data

            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting PO content from email {email_id}: {str(e)}")
            return None
    
    def _save_po_content(self, po_data: Dict) -> Optional[str]:
        """Save PO content to text file"""
        try:
            # Create output directory if it doesn't exist
            output_dir = self.config["processing"]["output_folder"]
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"PO_{timestamp}_{po_data['email_id']}.txt"
            filepath = os.path.join(output_dir, filename)
            
            # Save content
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"=== EMAIL METADATA ===\n")
                f.write(f"Email ID: {po_data['email_id']}\n")
                f.write(f"Timestamp: {po_data['timestamp']}\n")
                f.write(f"Sender: {po_data['sender']}\n")
                f.write(f"Subject: {po_data['subject']}\n")
                f.write(f"Date: {po_data['date']}\n")
                f.write(f"Attachments: {', '.join(po_data['attachments'])}\n")
                f.write(f"\n=== EMAIL BODY ===\n")
                f.write(po_data['body'])

                # Add text attachment content if any
                if po_data['text_attachments']:
                    f.write(f"\n\n=== TEXT ATTACHMENTS ===\n")
                    for filename, content in po_data['text_attachments'].items():
                        f.write(f"\n--- ATTACHMENT: {filename} ---\n")
                        f.write(content)
                        f.write(f"\n--- END OF {filename} ---\n")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error saving PO content: {str(e)}")
            return None
    
    def _save_processed_email_metadata(self, po_data: Dict, po_file_path: str) -> Optional[str]:
        """Save processed email metadata to processed_emails folder"""
        try:
            # Create processed emails directory
            processed_emails_dir = "data/processed_emails"
            os.makedirs(processed_emails_dir, exist_ok=True)

            # Generate metadata filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            metadata_filename = f"EMAIL_{timestamp}_{po_data['email_id']}.json"
            metadata_path = os.path.join(processed_emails_dir, metadata_filename)

            # Create metadata object
            email_metadata = {
                "email_processing": {
                    "email_id": po_data['email_id'],
                    "processed_at": datetime.now().isoformat(),
                    "sender": po_data['sender'],
                    "subject": po_data['subject'],
                    "date": po_data['date'],
                    "attachments": po_data['attachments'],
                    "has_text_attachments": bool(po_data['text_attachments']),
                    "text_attachment_count": len(po_data['text_attachments'])
                },
                "po_extraction": {
                    "po_file_created": os.path.basename(po_file_path),
                    "po_file_path": po_file_path,
                    "extraction_status": "success",
                    "workflow_stage": "extracted"
                },
                "workflow_tracking": {
                    "stage": "email_processed",
                    "next_stage": "awaiting_agent_processing",
                    "created_at": datetime.now().isoformat()
                }
            }

            # Save metadata
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(email_metadata, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Saved email metadata to: {metadata_path}")
            return metadata_path

        except Exception as e:
            self.logger.error(f"Error saving email metadata: {str(e)}")
            return None

    def mark_email_processed(self, email_id: str):
        """Mark email as read/processed"""
        try:
            self.mail_connection.store(email_id, '+FLAGS', '\\Seen')
            self.logger.debug(f"Marked email {email_id} as processed")
        except Exception as e:
            self.logger.error(f"Error marking email as processed: {str(e)}")
    
    def run_monitoring_cycle(self):
        """Run one cycle of email monitoring"""
        self.logger.info("Starting email monitoring cycle...")
        
        if not self.connect_to_gmail():
            return
        
        try:
            # Search for PO emails
            po_email_ids = self.search_po_emails()
            
            # Process each PO email
            for email_id in po_email_ids:
                self.logger.info(f"Processing email ID: {email_id}")
                
                # Extract PO content
                po_data = self.extract_po_content(email_id)
                
                if po_data:
                    # Mark as processed
                    self.mark_email_processed(email_id)
                    self.logger.info(f"Successfully processed email {email_id}")
                else:
                    self.logger.warning(f"Failed to extract content from email {email_id}")
            
        finally:
            self.disconnect()
    
    def start_monitoring(self):
        """Start continuous email monitoring"""
        self.logger.info("Starting continuous email monitoring...")
        
        polling_interval = self.config["processing"]["polling_interval"]
        
        try:
            while True:
                self.run_monitoring_cycle()
                self.logger.info(f"Waiting {polling_interval} seconds before next check...")
                time.sleep(polling_interval)
                
        except KeyboardInterrupt:
            self.logger.info("Email monitoring stopped by user")
        except Exception as e:
            self.logger.error(f"Email monitoring error: {str(e)}")


if __name__ == "__main__":
    # Create necessary directories
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data/incoming_pos", exist_ok=True)
    os.makedirs("config", exist_ok=True)
    
    # Initialize and start email monitor
    monitor = EmailMonitor()
    monitor.start_monitoring()
