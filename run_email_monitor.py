#!/usr/bin/env python3
"""
Simple runner script for O2C Email Monitoring Service
Provides easy commands to setup, test, and run the email monitor
"""

import os
import sys
import argparse
import subprocess


def setup_environment():
    """Create necessary directories and check dependencies"""
    print("Setting up O2C Email Monitor environment...")
    
    # Create directories
    directories = [
        "logs",
        "data/incoming_pos",
        "data/processed_emails",
        "config"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    # Check if config exists
    if not os.path.exists("config/email_config.json"):
        print("\n⚠ Configuration file not found!")
        print("Run: python run_email_monitor.py --setup")
        return False
    
    print("✓ Environment setup complete")
    return True


def run_setup():
    """Run the Gmail setup script"""
    print("Running Gmail setup...")
    try:
        subprocess.run([sys.executable, "scripts/setup_gmail.py"], check=True)
    except subprocess.CalledProcessError:
        print("Setup failed. Please check the error messages above.")
        return False
    except FileNotFoundError:
        print("Setup script not found. Please ensure scripts/setup_gmail.py exists.")
        return False
    return True


def run_tests():
    """Run the test suite"""
    print("Running email monitor tests...")
    try:
        # Run unit tests
        subprocess.run([sys.executable, "tests/test_email_monitor.py", "--unit"], check=True)
        print("✓ Unit tests passed")
        
        # Show manual test instructions
        subprocess.run([sys.executable, "tests/test_email_monitor.py", "--manual"])
        
    except subprocess.CalledProcessError:
        print("Tests failed. Please check the error messages above.")
        return False
    except FileNotFoundError:
        print("Test script not found. Please ensure tests/test_email_monitor.py exists.")
        return False
    return True


def run_single_check():
    """Run a single email monitoring cycle"""
    print("Running single email monitoring cycle...")
    try:
        from services.email_monitor import EmailMonitor
        monitor = EmailMonitor()
        monitor.run_monitoring_cycle()
        print("✓ Single check completed")
    except Exception as e:
        print(f"Single check failed: {str(e)}")
        return False
    return True


def run_continuous():
    """Run continuous email monitoring"""
    print("Starting continuous email monitoring...")
    print("Press Ctrl+C to stop")
    try:
        from services.email_monitor import EmailMonitor
        monitor = EmailMonitor()
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n✓ Email monitoring stopped by user")
    except Exception as e:
        print(f"Continuous monitoring failed: {str(e)}")
        return False
    return True


def show_status():
    """Show current status and configuration"""
    print("=== O2C Email Monitor Status ===\n")
    
    # Check environment
    if setup_environment():
        print("✓ Environment: Ready")
    else:
        print("✗ Environment: Not configured")
        return
    
    # Check configuration
    try:
        import json
        with open("config/email_config.json", 'r') as f:
            config = json.load(f)
        
        print(f"✓ Gmail Account: {config.get('gmail', {}).get('username', 'Not configured')}")
        print(f"✓ Polling Interval: {config.get('processing', {}).get('polling_interval', 30)} seconds")
        
        # Check detection settings
        po_config = config.get('po_detection', {})
        print(f"✓ Subject Keywords: {len(po_config.get('subject_keywords', []))} configured")
        print(f"✓ Sender Domains: {len(po_config.get('sender_domains', []))} configured")
        print(f"✓ Body Keywords: {len(po_config.get('body_keywords', []))} configured")
        
    except Exception as e:
        print(f"✗ Configuration: Error reading config - {str(e)}")
        return
    
    # Check output directory
    output_dir = "data/incoming_pos"
    if os.path.exists(output_dir):
        po_files = [f for f in os.listdir(output_dir) if f.endswith('.txt')]
        print(f"✓ Extracted POs: {len(po_files)} files in {output_dir}")
    else:
        print(f"✗ Output directory not found: {output_dir}")
    
    # Check logs
    log_file = "logs/email_monitor.log"
    if os.path.exists(log_file):
        log_size = os.path.getsize(log_file)
        print(f"✓ Log file: {log_file} ({log_size} bytes)")
    else:
        print(f"✗ Log file not found: {log_file}")


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="O2C Email Monitoring Service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_email_monitor.py --setup          # Initial Gmail setup
  python run_email_monitor.py --test           # Run tests
  python run_email_monitor.py --check          # Single email check
  python run_email_monitor.py --run            # Continuous monitoring
  python run_email_monitor.py --status         # Show current status
        """
    )
    
    parser.add_argument("--setup", action="store_true", 
                       help="Run Gmail setup and configuration")
    parser.add_argument("--test", action="store_true", 
                       help="Run test suite")
    parser.add_argument("--check", action="store_true", 
                       help="Run single email monitoring cycle")
    parser.add_argument("--run", action="store_true", 
                       help="Start continuous email monitoring")
    parser.add_argument("--status", action="store_true", 
                       help="Show current status and configuration")
    
    args = parser.parse_args()
    
    if args.setup:
        setup_environment()
        run_setup()
    elif args.test:
        setup_environment()
        run_tests()
    elif args.check:
        setup_environment()
        run_single_check()
    elif args.run:
        setup_environment()
        run_continuous()
    elif args.status:
        show_status()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
