#!/usr/bin/env python3
"""
Quick script to set DeepSeek API key for testing
"""

import os
import getpass


def set_deepseek_key():
    """Set DeepSeek API key in environment"""
    print("=== DeepSeek API Key Setup ===\n")
    
    print("Get your DeepSeek API key from: https://platform.deepseek.com/api_keys")
    print("Note: You need to create an account and may need to add credits\n")
    
    # Check if already set
    existing_key = os.getenv("DEEPSEEK_API_KEY")
    if existing_key:
        print(f"✅ DEEPSEEK_API_KEY is already set (length: {len(existing_key)} characters)")
        use_existing = input("Use existing key? (y/n): ").lower().strip()
        if use_existing == 'y':
            return existing_key
    
    # Get new API key
    api_key = getpass.getpass("Enter your DeepSeek API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided!")
        return None
    
    # Set in current environment
    os.environ["DEEPSEEK_API_KEY"] = api_key
    
    print("✅ DeepSeek API key set for current session")
    print("\n📝 To make this permanent, add to your shell profile:")
    print(f"export DEEPSEEK_API_KEY='{api_key}'")
    
    return api_key


if __name__ == "__main__":
    key = set_deepseek_key()
    
    if key:
        print("\n🚀 Ready to test CrewAI O2C System!")
        print("Run: python3 test_crewai_setup.py")
    else:
        print("\n❌ Setup incomplete. Please try again.")
