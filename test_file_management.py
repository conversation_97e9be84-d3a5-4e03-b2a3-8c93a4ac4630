#!/usr/bin/env python3
"""
Test the improved file management workflow
"""

import json
import os
import shutil
from datetime import datetime

# Sample parsed PO data (from the agent's output)
sample_po_data = {
    "po_number": "PO-TEST-2024-001",
    "po_date": "2024-07-08",
    "vendor": {
        "name": "Test Supplier Inc.",
        "address": "789 Vendor Avenue",
        "city": "Supplier City",
        "state": "TX",
        "zip": "75001",
        "phone": "(*************",
        "email": "<EMAIL>"
    },
    "customer": {
        "name": "ABC Manufacturing Corp",
        "address": "123 Business Street",
        "city": "Anytown",
        "state": "CA",
        "zip": "90210",
        "phone": "(*************",
        "email": "<EMAIL>"
    },
    "line_items": [
        {
            "item_code": "WIDGET-A100",
            "description": "Premium Widget Assembly Type A",
            "quantity": 50,
            "unit": "units",
            "unit_price": 125.00,
            "line_total": 6250.00
        },
        {
            "item_code": "BOLT-M8-50",
            "description": "M8 x 50mm Stainless Steel Bolts",
            "quantity": 200,
            "unit": "pieces",
            "unit_price": 2.50,
            "line_total": 500.00
        }
    ],
    "pricing_summary": {
        "subtotal": 8024.75,
        "tax_rate": 8.25,
        "tax_amount": 662.04,
        "shipping_handling": 125.00,
        "total_amount": 8811.79
    },
    "validation": {
        "status": "passed",
        "message": "All required fields present and valid"
    }
}

def manual_po_data_saver(po_data: str, original_filename: str = "") -> str:
    """Manual implementation of po_data_saver for testing"""
    try:
        # Create output directories
        parsed_dir = "data/parsed_pos"
        processed_dir = "data/processed_pos"
        os.makedirs(parsed_dir, exist_ok=True)
        os.makedirs(processed_dir, exist_ok=True)

        # Parse the PO data
        try:
            parsed_data = json.loads(po_data)
        except json.JSONDecodeError:
            return "Error: Invalid JSON format in PO data"

        # Generate output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if original_filename:
            base_name = original_filename.replace('.txt', '').replace('PO_', '')
            output_filename = f"PARSED_{base_name}.json"
        else:
            output_filename = f"PARSED_PO_{timestamp}.json"

        output_path = os.path.join(parsed_dir, output_filename)

        # Add metadata
        parsed_data["parsing_metadata"] = {
            "parsed_at": datetime.now().isoformat(),
            "original_file": original_filename,
            "parser_version": "1.0",
            "workflow_stage": "parsed"
        }

        # Save to JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(parsed_data, f, indent=2, ensure_ascii=False)

        # Move original file to processed folder
        file_management_msg = ""
        if original_filename:
            source_path = os.path.join("data/incoming_pos", original_filename)
            dest_path = os.path.join(processed_dir, original_filename)

            if os.path.exists(source_path):
                shutil.move(source_path, dest_path)
                file_management_msg = f"\n✅ Moved original file: {source_path} → {dest_path}"
            else:
                file_management_msg = f"\n⚠️ Original file not found: {source_path}"

        return f"✅ Successfully saved parsed PO data to: {output_path}{file_management_msg}"

    except Exception as e:
        return f"❌ Error saving PO data: {str(e)}"

def test_file_management():
    """Test the file management workflow"""
    print("🧪 Testing File Management Workflow")
    print("=" * 50)

    # Convert to JSON string
    po_json = json.dumps(sample_po_data, indent=2)

    # Test the po_data_saver tool
    print("📄 Testing po_data_saver with file movement...")
    result = manual_po_data_saver(po_json, "PO_20250708_220055_25.txt")
    print(f"Result: {result}")
    
    print("\n📊 Checking folder contents after processing...")
    
    print("\n📁 data/incoming_pos/:")
    incoming_files = os.listdir("data/incoming_pos") if os.path.exists("data/incoming_pos") else []
    for file in incoming_files:
        print(f"  - {file}")
    if not incoming_files:
        print("  (empty)")
    
    print("\n📁 data/parsed_pos/:")
    parsed_files = os.listdir("data/parsed_pos") if os.path.exists("data/parsed_pos") else []
    for file in parsed_files:
        print(f"  - {file}")
    if not parsed_files:
        print("  (empty)")
    
    print("\n📁 data/processed_pos/:")
    processed_files = os.listdir("data/processed_pos") if os.path.exists("data/processed_pos") else []
    for file in processed_files:
        print(f"  - {file}")
    if not processed_files:
        print("  (empty)")

if __name__ == "__main__":
    test_file_management()
