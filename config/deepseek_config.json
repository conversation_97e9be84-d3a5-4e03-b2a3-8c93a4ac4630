{"deepseek": {"api_key": "", "model_name": "deepseek-chat", "base_url": "https://api.deepseek.com/v1/chat/completions", "temperature": 0.1, "max_tokens": 4000, "timeout": 60}, "crewai": {"verbose": true, "memory": true, "max_iterations": 3}, "po_parsing": {"input_folder": "data/incoming_pos", "output_folder": "data/parsed_pos", "backup_folder": "data/processed_pos", "required_fields": ["po_number", "date", "vendor", "customer", "line_items", "pricing"], "date_formats": ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y", "%B %d, %Y", "%b %d, %Y"]}}