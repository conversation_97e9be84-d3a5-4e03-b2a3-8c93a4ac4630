{"gmail": {"username": "<EMAIL>", "app_password": "cmkd sqdx prqn yorm", "imap_server": "imap.gmail.com", "imap_port": 993}, "po_detection": {"subject_keywords": ["PO", "Purchase Order", "P.O.", "order", "purchase", "procurement", "requisition"], "sender_domains": ["@gmail.com", "@supplier.com", "@vendor.com", "@procurement.com", "@purchasing.com"], "body_keywords": ["purchase order", "po number", "order number", "purchase order number", "p.o. number", "order id", "procurement", "requisition number", "vendor", "supplier", "delivery date", "ship to", "bill to", "quantity", "unit price", "total amount"]}, "processing": {"output_folder": "data/incoming_pos", "processed_folder": "data/processed_emails", "polling_interval": 30, "max_emails_per_cycle": 10, "automated_workflow": true, "workflow_timeout": 300}, "logging": {"level": "INFO", "log_file": "logs/email_monitor.log", "max_log_size": "10MB", "backup_count": 5}}