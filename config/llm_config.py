#!/usr/bin/env python3
"""
LLM Configuration for O2C Agents
Provides standardized LLM configuration for all agents
"""

import os
import json
from pathlib import Path
from typing import Dict, Any

def get_deepseek_llm():
    """Get configured DeepSeek LLM instance for O2C agents"""
    
    try:
        # Load DeepSeek configuration
        config_path = Path(__file__).parent / "deepseek_config.json"
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            # Default configuration
            config = {
                "model": "deepseek/deepseek-chat",
                "temperature": 0.4,
                "max_tokens": 4000
            }
        
        # Get API key from environment or config
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key and "api_key" in config:
            api_key = config["api_key"]
        
        if not api_key:
            raise ValueError("DeepSeek API key not found in environment or config")
        
        # Create LLM configuration for CrewAI
        llm_config = {
            "model": config.get("model", "deepseek/deepseek-chat"),
            "temperature": config.get("temperature", 0.4),
            "max_tokens": config.get("max_tokens", 4000),
            "api_key": api_key
        }
        
        return llm_config
        
    except Exception as e:
        print(f"Warning: Failed to load DeepSeek config: {e}")
        # Return default configuration
        return {
            "model": "deepseek/deepseek-chat",
            "temperature": 0.4,
            "max_tokens": 4000,
            "api_key": os.getenv("DEEPSEEK_API_KEY", "")
        }

def get_llm_for_crewai():
    """Get LLM instance compatible with CrewAI"""

    try:
        from crewai import LLM
        import os

        # Get API key from environment or config
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            config = get_deepseek_llm()
            api_key = config.get("api_key", "")

        if not api_key:
            raise ValueError("DeepSeek API key not found")

        # Create CrewAI LLM instance with proper model specification
        llm = LLM(
            model="deepseek/deepseek-chat",  # Explicit provider/model format
            temperature=0.4,
            max_tokens=4000,
            api_key=api_key
        )

        return llm

    except ImportError:
        print("Warning: CrewAI not available, returning config dict")
        return get_deepseek_llm()
    except Exception as e:
        print(f"Warning: Failed to create CrewAI LLM: {e}")
        # Fallback to basic config
        return get_deepseek_llm()

# For backward compatibility
def get_deepseek_config():
    """Legacy function for backward compatibility"""
    return get_deepseek_llm()
