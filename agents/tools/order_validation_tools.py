#!/usr/bin/env python3
"""
Order Validation Tools for Comprehensive Order Validation Agent
Multi-layer validation with business rules and compliance checks
"""

import os
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pydantic import BaseModel, Field
from crewai.tools import tool


@tool
def parsed_order_reader(filename: str = "") -> str:
    """Read parsed order JSON file from data/parsed_pos folder

    Args:
        filename: Name of the parsed order file to read

    Returns:
        JSON string containing the parsed order data
    """
    try:
        if not filename:
            # Find the most recent parsed order file
            parsed_dir = "data/parsed_pos"
            if os.path.exists(parsed_dir):
                files = [f for f in os.listdir(parsed_dir) if f.startswith("PARSED_") and f.endswith(".json")]
                if files:
                    files.sort(reverse=True)  # Most recent first
                    filename = files[0]
                else:
                    return json.dumps({
                        "status": "ERROR",
                        "message": "No parsed order files found in data/parsed_pos/"
                    })
            else:
                return json.dumps({
                    "status": "ERROR",
                    "message": "Parsed orders directory not found"
                })

        # Read the file
        file_path = os.path.join("data/parsed_pos", filename)
        if not os.path.exists(file_path):
            return json.dumps({
                "status": "ERROR",
                "message": f"File not found: {file_path}"
            })

        with open(file_path, 'r', encoding='utf-8') as f:
            order_data = json.load(f)

        return json.dumps({
            "status": "SUCCESS",
            "filename": filename,
            "order_data": order_data
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error reading parsed order file: {str(e)}"
        })


# Mock databases for validation (in production, these would be real database connections)
CUSTOMER_DATABASE = {
    "ABC Manufacturing Corp": {
        "customer_id": "CUST-001",
        "status": "ACTIVE",
        "credit_limit": 50000.00,
        "payment_terms": "Net 30",
        "risk_score": "LOW",
        "approved_addresses": [
            "123 Business Street, Anytown, CA 90210",
            "456 Industrial Blvd, Anytown, CA 90211"
        ],
        "tax_exempt": False,
        "currency": "USD"
    },
    "Test Supplier Inc.": {
        "customer_id": "CUST-002", 
        "status": "ACTIVE",
        "credit_limit": 25000.00,
        "payment_terms": "Net 15",
        "risk_score": "MEDIUM",
        "approved_addresses": [
            "789 Vendor Avenue, Supplier City, TX 75001"
        ],
        "tax_exempt": True,
        "currency": "USD"
    }
}

PRODUCT_CATALOG = {
    "WIDGET-A100": {
        "description": "Premium Widget Assembly Type A",
        "status": "ACTIVE",
        "unit_price": 125.00,
        "category": "WIDGETS",
        "tax_code": "TAXABLE",
        "minimum_order": 10,
        "maximum_discount": 15.0
    },
    "BOLT-M8-50": {
        "description": "M8 x 50mm Stainless Steel Bolts",
        "status": "ACTIVE", 
        "unit_price": 2.50,
        "category": "HARDWARE",
        "tax_code": "TAXABLE",
        "minimum_order": 50,
        "maximum_discount": 10.0
    },
    "GASKET-RUBBER-12": {
        "description": "12mm Rubber Gasket",
        "status": "DISCONTINUED",
        "unit_price": 5.75,
        "category": "SEALS",
        "tax_code": "TAXABLE",
        "minimum_order": 25,
        "maximum_discount": 5.0
    }
}

BUSINESS_RULES = {
    "minimum_order_value": 100.00,
    "maximum_order_value": 100000.00,
    "required_fields": ["po_number", "customer", "line_items", "pricing_summary"],
    "delivery_lead_time_days": 14,
    "maximum_line_items": 50,
    "supported_currencies": ["USD", "EUR", "CAD"],
    "tax_rates": {
        "CA": 8.25,
        "TX": 6.25,
        "NY": 8.0
    }
}


@tool
def customer_validator(customer_name: str, order_total: float = 0.0) -> str:
    """Validate customer existence, status, and credit eligibility
    
    Args:
        customer_name: Name of the customer to validate
        order_total: Total order amount for credit check
        
    Returns:
        Validation result with customer status and recommendations
    """
    try:
        # Clean customer name for lookup
        customer_key = customer_name.strip()
        
        # Check if customer exists
        if customer_key not in CUSTOMER_DATABASE:
            return json.dumps({
                "status": "FAIL",
                "validation_type": "customer_existence",
                "message": f"Customer '{customer_name}' not found in database",
                "recommendations": ["Verify customer name spelling", "Check if new customer registration required"],
                "risk_level": "HIGH"
            })
        
        customer = CUSTOMER_DATABASE[customer_key]
        
        # Check customer status
        if customer["status"] != "ACTIVE":
            return json.dumps({
                "status": "FAIL",
                "validation_type": "customer_status",
                "message": f"Customer '{customer_name}' has status: {customer['status']}",
                "recommendations": ["Contact customer service", "Verify account status"],
                "risk_level": "HIGH"
            })
        
        # Check credit limit if order total provided
        credit_issues = []
        if order_total > 0:
            if order_total > customer["credit_limit"]:
                credit_issues.append(f"Order total ${order_total:,.2f} exceeds credit limit ${customer['credit_limit']:,.2f}")
        
        # Determine overall status
        if credit_issues:
            return json.dumps({
                "status": "REVIEW",
                "validation_type": "customer_credit",
                "message": f"Customer validation passed with credit concerns",
                "customer_info": customer,
                "credit_issues": credit_issues,
                "recommendations": ["Require credit approval", "Consider payment terms adjustment"],
                "risk_level": customer["risk_score"]
            })
        
        return json.dumps({
            "status": "PASS",
            "validation_type": "customer_validation",
            "message": f"Customer '{customer_name}' validated successfully",
            "customer_info": customer,
            "risk_level": customer["risk_score"]
        })
        
    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "validation_type": "customer_validation",
            "message": f"Error validating customer: {str(e)}",
            "risk_level": "HIGH"
        })


@tool
def product_catalog_validator(line_items: str) -> str:
    """Validate product codes, pricing, and catalog alignment
    
    Args:
        line_items: JSON string containing line items to validate
        
    Returns:
        Validation result with product status and pricing verification
    """
    try:
        # Parse line items
        try:
            items = json.loads(line_items)
        except json.JSONDecodeError:
            return json.dumps({
                "status": "ERROR",
                "validation_type": "product_validation",
                "message": "Invalid JSON format for line items"
            })
        
        validation_results = []
        total_issues = 0
        
        for i, item in enumerate(items):
            item_code = item.get("item_code") or item.get("product_code") or item.get("sku")
            quantity = item.get("quantity", 0)
            unit_price = item.get("unit_price", 0.0)
            
            if not item_code:
                validation_results.append({
                    "line_number": i + 1,
                    "status": "FAIL",
                    "message": "Missing product code",
                    "recommendations": ["Add valid product code"]
                })
                total_issues += 1
                continue
            
            # Check if product exists in catalog
            if item_code not in PRODUCT_CATALOG:
                validation_results.append({
                    "line_number": i + 1,
                    "item_code": item_code,
                    "status": "FAIL",
                    "message": f"Product code '{item_code}' not found in catalog",
                    "recommendations": ["Verify product code", "Check for typos", "Contact product team"]
                })
                total_issues += 1
                continue
            
            product = PRODUCT_CATALOG[item_code]
            
            # Check product status
            if product["status"] != "ACTIVE":
                validation_results.append({
                    "line_number": i + 1,
                    "item_code": item_code,
                    "status": "FAIL",
                    "message": f"Product '{item_code}' is {product['status']}",
                    "recommendations": ["Find alternative product", "Contact product team"]
                })
                total_issues += 1
                continue
            
            # Check minimum order quantity
            if quantity < product["minimum_order"]:
                validation_results.append({
                    "line_number": i + 1,
                    "item_code": item_code,
                    "status": "REVIEW",
                    "message": f"Quantity {quantity} below minimum order {product['minimum_order']}",
                    "recommendations": [f"Increase quantity to {product['minimum_order']} or more"]
                })
            
            # Check pricing
            catalog_price = product["unit_price"]
            price_variance = abs(unit_price - catalog_price) / catalog_price * 100
            
            if price_variance > 5.0:  # More than 5% variance
                validation_results.append({
                    "line_number": i + 1,
                    "item_code": item_code,
                    "status": "REVIEW",
                    "message": f"Price variance: Order ${unit_price:.2f} vs Catalog ${catalog_price:.2f} ({price_variance:.1f}%)",
                    "recommendations": ["Verify pricing", "Check for special discounts"]
                })
            
            # If all checks pass
            if not any(r["line_number"] == i + 1 and r["status"] in ["FAIL", "REVIEW"] for r in validation_results):
                validation_results.append({
                    "line_number": i + 1,
                    "item_code": item_code,
                    "status": "PASS",
                    "message": "Product validation passed",
                    "catalog_info": product
                })
        
        # Determine overall status
        failed_items = sum(1 for r in validation_results if r["status"] == "FAIL")
        review_items = sum(1 for r in validation_results if r["status"] == "REVIEW")
        
        if failed_items > 0:
            overall_status = "FAIL"
        elif review_items > 0:
            overall_status = "REVIEW"
        else:
            overall_status = "PASS"
        
        return json.dumps({
            "status": overall_status,
            "validation_type": "product_catalog",
            "message": f"Validated {len(items)} line items: {failed_items} failed, {review_items} need review",
            "line_item_results": validation_results,
            "summary": {
                "total_items": len(items),
                "passed": len(items) - failed_items - review_items,
                "failed": failed_items,
                "review_required": review_items
            }
        })
        
    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "validation_type": "product_catalog",
            "message": f"Error validating products: {str(e)}"
        })


@tool
def business_rules_validator(order_data: str) -> str:
    """Apply business rule validations to order data
    
    Args:
        order_data: JSON string containing complete order data
        
    Returns:
        Business rules validation result
    """
    try:
        # Parse order data
        try:
            order = json.loads(order_data)
        except json.JSONDecodeError:
            return json.dumps({
                "status": "ERROR",
                "validation_type": "business_rules",
                "message": "Invalid JSON format for order data"
            })
        
        violations = []
        warnings = []
        
        # Check required fields
        for field in BUSINESS_RULES["required_fields"]:
            if field not in order:
                violations.append(f"Missing required field: {field}")
        
        # Check order value limits
        pricing = order.get("pricing_summary") or order.get("pricing", {})
        total_amount = pricing.get("total_amount") or pricing.get("total", 0.0)
        
        if total_amount < BUSINESS_RULES["minimum_order_value"]:
            violations.append(f"Order total ${total_amount:.2f} below minimum ${BUSINESS_RULES['minimum_order_value']:.2f}")
        
        if total_amount > BUSINESS_RULES["maximum_order_value"]:
            violations.append(f"Order total ${total_amount:.2f} exceeds maximum ${BUSINESS_RULES['maximum_order_value']:.2f}")
        
        # Check line item count
        line_items = order.get("line_items", [])
        if len(line_items) > BUSINESS_RULES["maximum_line_items"]:
            violations.append(f"Too many line items: {len(line_items)} (max: {BUSINESS_RULES['maximum_line_items']})")
        
        # Check delivery date
        delivery = order.get("delivery", {})
        requested_date = delivery.get("requested_date")
        if requested_date:
            try:
                req_date = datetime.fromisoformat(requested_date.replace('Z', '+00:00'))
                min_date = datetime.now() + timedelta(days=BUSINESS_RULES["delivery_lead_time_days"])
                if req_date < min_date:
                    warnings.append(f"Requested delivery date may not meet {BUSINESS_RULES['delivery_lead_time_days']}-day lead time")
            except:
                warnings.append("Invalid delivery date format")
        
        # Determine overall status
        if violations:
            status = "FAIL"
        elif warnings:
            status = "REVIEW"
        else:
            status = "PASS"
        
        return json.dumps({
            "status": status,
            "validation_type": "business_rules",
            "message": f"Business rules validation: {len(violations)} violations, {len(warnings)} warnings",
            "violations": violations,
            "warnings": warnings,
            "rules_applied": list(BUSINESS_RULES.keys()),
            "order_summary": {
                "total_amount": total_amount,
                "line_item_count": len(line_items),
                "currency": order.get("currency", "USD")
            }
        })
        
    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "validation_type": "business_rules",
            "message": f"Error validating business rules: {str(e)}"
        })


@tool
def delivery_address_validator(delivery_info: str, customer_name: str = "") -> str:
    """Validate delivery addresses and shipping restrictions

    Args:
        delivery_info: JSON string containing delivery information
        customer_name: Customer name for approved address verification

    Returns:
        Delivery validation result with address verification
    """
    try:
        # Parse delivery info
        try:
            delivery = json.loads(delivery_info)
        except json.JSONDecodeError:
            return json.dumps({
                "status": "ERROR",
                "validation_type": "delivery_validation",
                "message": "Invalid JSON format for delivery information"
            })

        issues = []
        warnings = []

        # Check required delivery fields
        address = delivery.get("address", "")
        if not address:
            issues.append("Missing delivery address")

        # Basic address format validation
        if address and len(address.strip()) < 10:
            issues.append("Delivery address appears incomplete")

        # Check for PO Box restrictions (example business rule)
        if "P.O. Box" in address or "PO Box" in address:
            warnings.append("PO Box delivery may have restrictions")

        # Validate against customer approved addresses
        if customer_name and customer_name in CUSTOMER_DATABASE:
            customer = CUSTOMER_DATABASE[customer_name]
            approved_addresses = customer.get("approved_addresses", [])

            address_approved = False
            for approved in approved_addresses:
                # Simple substring matching (in production, use proper address matching)
                if any(part.lower() in address.lower() for part in approved.split(", ")[:2]):
                    address_approved = True
                    break

            if not address_approved and approved_addresses:
                warnings.append("Delivery address not in customer's approved address list")

        # Check delivery date
        requested_date = delivery.get("requested_date")
        if requested_date:
            try:
                req_date = datetime.fromisoformat(requested_date.replace('Z', '+00:00'))
                if req_date < datetime.now():
                    issues.append("Requested delivery date is in the past")
                elif req_date < datetime.now() + timedelta(days=1):
                    warnings.append("Very short delivery timeframe requested")
            except:
                issues.append("Invalid delivery date format")

        # Determine status
        if issues:
            status = "FAIL"
        elif warnings:
            status = "REVIEW"
        else:
            status = "PASS"

        return json.dumps({
            "status": status,
            "validation_type": "delivery_validation",
            "message": f"Delivery validation: {len(issues)} issues, {len(warnings)} warnings",
            "issues": issues,
            "warnings": warnings,
            "delivery_info": delivery
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "validation_type": "delivery_validation",
            "message": f"Error validating delivery: {str(e)}"
        })


@tool
def compliance_validator(order_data: str) -> str:
    """Perform regulatory compliance checks

    Args:
        order_data: JSON string containing complete order data

    Returns:
        Compliance validation result
    """
    try:
        # Parse order data
        try:
            order = json.loads(order_data)
        except json.JSONDecodeError:
            return json.dumps({
                "status": "ERROR",
                "validation_type": "compliance",
                "message": "Invalid JSON format for order data"
            })

        compliance_issues = []
        compliance_warnings = []

        # Check for required compliance fields
        if not order.get("po_number"):
            compliance_issues.append("Missing PO number required for audit trail")

        # Tax compliance checks
        customer_info = order.get("customer", {})
        delivery_info = order.get("delivery", {})

        # Check tax calculation requirements
        pricing = order.get("pricing_summary") or order.get("pricing", {})
        tax_amount = pricing.get("tax_amount", 0.0)
        subtotal = pricing.get("subtotal", 0.0)

        if subtotal > 0 and tax_amount == 0:
            # Check if customer is tax exempt
            customer_name = customer_info.get("name", "")
            if customer_name in CUSTOMER_DATABASE:
                customer = CUSTOMER_DATABASE[customer_name]
                if not customer.get("tax_exempt", False):
                    compliance_warnings.append("No tax calculated for non-exempt customer")

        # Export control checks (simplified)
        line_items = order.get("line_items", [])
        for item in line_items:
            item_code = item.get("item_code", "")
            if "EXPORT" in item_code.upper():
                compliance_warnings.append(f"Item {item_code} may require export license")

        # Data privacy compliance
        if not order.get("parsing_metadata", {}).get("parsed_at"):
            compliance_warnings.append("Missing processing timestamp for audit trail")

        # Financial compliance
        total_amount = pricing.get("total_amount") or pricing.get("total", 0.0)
        if total_amount > 10000:  # Example threshold
            compliance_warnings.append("High-value order may require additional approval")

        # Determine status
        if compliance_issues:
            status = "FAIL"
        elif compliance_warnings:
            status = "REVIEW"
        else:
            status = "PASS"

        return json.dumps({
            "status": status,
            "validation_type": "compliance",
            "message": f"Compliance validation: {len(compliance_issues)} issues, {len(compliance_warnings)} warnings",
            "compliance_issues": compliance_issues,
            "compliance_warnings": compliance_warnings,
            "checks_performed": [
                "audit_trail_requirements",
                "tax_compliance",
                "export_control",
                "data_privacy",
                "financial_thresholds"
            ]
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "validation_type": "compliance",
            "message": f"Error performing compliance checks: {str(e)}"
        })


@tool
def risk_assessment_calculator(validation_results: str, customer_name: str = "") -> str:
    """Calculate overall risk score based on validation results

    Args:
        validation_results: JSON string containing all validation results
        customer_name: Customer name for risk profile lookup

    Returns:
        Risk assessment with overall score and recommendations
    """
    try:
        # Parse validation results
        try:
            results = json.loads(validation_results)
        except json.JSONDecodeError:
            return json.dumps({
                "status": "ERROR",
                "validation_type": "risk_assessment",
                "message": "Invalid JSON format for validation results"
            })

        risk_score = 0
        risk_factors = []

        # Base customer risk
        if customer_name and customer_name in CUSTOMER_DATABASE:
            customer_risk = CUSTOMER_DATABASE[customer_name].get("risk_score", "MEDIUM")
            if customer_risk == "HIGH":
                risk_score += 30
                risk_factors.append("High-risk customer profile")
            elif customer_risk == "MEDIUM":
                risk_score += 15
                risk_factors.append("Medium-risk customer profile")
            else:
                risk_score += 5
        else:
            risk_score += 25
            risk_factors.append("Unknown customer")

        # Validation failure penalties
        for validation_type, result in results.items():
            if isinstance(result, dict):
                status = result.get("status", "UNKNOWN")
                if status == "FAIL":
                    risk_score += 25
                    risk_factors.append(f"Failed {validation_type} validation")
                elif status == "REVIEW":
                    risk_score += 10
                    risk_factors.append(f"{validation_type} requires review")
                elif status == "ERROR":
                    risk_score += 20
                    risk_factors.append(f"Error in {validation_type} validation")

        # Determine risk level
        if risk_score >= 70:
            risk_level = "HIGH"
            recommendation = "REJECT or require manual approval"
        elif risk_score >= 40:
            risk_level = "MEDIUM"
            recommendation = "REVIEW required before approval"
        elif risk_score >= 20:
            risk_level = "LOW-MEDIUM"
            recommendation = "APPROVE with monitoring"
        else:
            risk_level = "LOW"
            recommendation = "APPROVE automatically"

        return json.dumps({
            "status": "COMPLETE",
            "validation_type": "risk_assessment",
            "risk_score": risk_score,
            "risk_level": risk_level,
            "recommendation": recommendation,
            "risk_factors": risk_factors,
            "assessment_details": {
                "total_validations": len(results),
                "failed_validations": sum(1 for r in results.values() if isinstance(r, dict) and r.get("status") == "FAIL"),
                "review_required": sum(1 for r in results.values() if isinstance(r, dict) and r.get("status") == "REVIEW"),
                "customer_risk_contribution": 30 if customer_name in CUSTOMER_DATABASE and CUSTOMER_DATABASE[customer_name].get("risk_score") == "HIGH" else 15
            }
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "validation_type": "risk_assessment",
            "message": f"Error calculating risk assessment: {str(e)}"
        })


def get_order_validation_tools():
    """Get all order validation tools for the agent"""
    return [
        parsed_order_reader,
        customer_validator,
        product_catalog_validator,
        business_rules_validator,
        delivery_address_validator,
        compliance_validator,
        risk_assessment_calculator
    ]
