#!/usr/bin/env python3
"""
Credit Management Tools
Advanced financial analysis and credit assessment tools
"""

import os
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from crewai.tools import tool


@tool
def customer_credit_analyzer(customer_name: str, order_amount: float = 0.0) -> str:
    """Analyze customer credit profile and financial stability
    
    Args:
        customer_name: Name of the customer to analyze
        order_amount: Current order amount for context
        
    Returns:
        Comprehensive credit analysis with scoring and recommendations
    """
    try:
        # Mock credit database - in production, this would connect to credit bureaus and internal systems
        credit_database = {
            "ABC Manufacturing Corp": {
                "credit_score": 785,
                "credit_rating": "A-",
                "established_date": "2018-03-15",
                "industry": "Manufacturing",
                "annual_revenue": 25000000,
                "employees": 150,
                "duns_number": "123456789",
                "payment_terms": "Net 30",
                "credit_limit": 50000,
                "current_balance": 12500,
                "available_credit": 37500,
                "last_credit_review": "2024-06-01",
                "financial_strength": "Strong",
                "industry_risk": "Medium",
                "geographic_risk": "Low",
                "concentration_risk": "Low"
            },
            "XYZ Corp": {
                "credit_score": 650,
                "credit_rating": "B+",
                "established_date": "2020-08-10",
                "industry": "Technology",
                "annual_revenue": 5000000,
                "employees": 45,
                "duns_number": "987654321",
                "payment_terms": "Net 15",
                "credit_limit": 25000,
                "current_balance": 18000,
                "available_credit": 7000,
                "last_credit_review": "2024-05-15",
                "financial_strength": "Moderate",
                "industry_risk": "High",
                "geographic_risk": "Medium",
                "concentration_risk": "Medium"
            }
        }
        
        customer_data = credit_database.get(customer_name)
        
        if not customer_data:
            # New customer - limited credit data
            return json.dumps({
                "status": "LIMITED_DATA",
                "customer_name": customer_name,
                "credit_analysis": {
                    "credit_score": None,
                    "credit_rating": "Unrated",
                    "risk_level": "HIGH",
                    "recommendation": "MANUAL_REVIEW",
                    "reason": "New customer - insufficient credit history",
                    "suggested_credit_limit": min(order_amount * 1.2, 10000),
                    "required_actions": [
                        "Obtain credit application",
                        "Request financial statements",
                        "Check trade references",
                        "Verify business registration"
                    ]
                }
            })
        
        # Calculate credit utilization
        utilization = (customer_data["current_balance"] / customer_data["credit_limit"]) * 100
        
        # Determine risk level based on multiple factors
        risk_factors = []
        risk_score = 0
        
        # Credit score assessment
        if customer_data["credit_score"] >= 750:
            risk_score += 10
        elif customer_data["credit_score"] >= 700:
            risk_score += 20
        elif customer_data["credit_score"] >= 650:
            risk_score += 35
            risk_factors.append("Moderate credit score")
        else:
            risk_score += 50
            risk_factors.append("Low credit score")
        
        # Credit utilization assessment
        if utilization > 80:
            risk_score += 25
            risk_factors.append("High credit utilization")
        elif utilization > 60:
            risk_score += 15
            risk_factors.append("Moderate credit utilization")
        
        # Industry risk assessment
        if customer_data["industry_risk"] == "High":
            risk_score += 20
            risk_factors.append("High industry risk")
        elif customer_data["industry_risk"] == "Medium":
            risk_score += 10
        
        # Order size relative to credit limit
        if order_amount > customer_data["available_credit"]:
            risk_score += 30
            risk_factors.append("Order exceeds available credit")
        elif order_amount > customer_data["available_credit"] * 0.8:
            risk_score += 15
            risk_factors.append("Order uses significant available credit")
        
        # Determine overall risk level
        if risk_score <= 25:
            risk_level = "LOW"
            recommendation = "APPROVE"
        elif risk_score <= 50:
            risk_level = "MEDIUM"
            recommendation = "APPROVE_WITH_CONDITIONS"
        elif risk_score <= 75:
            risk_level = "HIGH"
            recommendation = "MANUAL_REVIEW"
        else:
            risk_level = "VERY_HIGH"
            recommendation = "DENY"
        
        return json.dumps({
            "status": "SUCCESS",
            "customer_name": customer_name,
            "credit_analysis": {
                "credit_score": customer_data["credit_score"],
                "credit_rating": customer_data["credit_rating"],
                "financial_strength": customer_data["financial_strength"],
                "industry": customer_data["industry"],
                "annual_revenue": customer_data["annual_revenue"],
                "established_date": customer_data["established_date"],
                "credit_limit": customer_data["credit_limit"],
                "current_balance": customer_data["current_balance"],
                "available_credit": customer_data["available_credit"],
                "credit_utilization": round(utilization, 2),
                "risk_score": risk_score,
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "recommendation": recommendation,
                "order_impact": {
                    "order_amount": order_amount,
                    "remaining_credit_after_order": customer_data["available_credit"] - order_amount,
                    "utilization_after_order": round(((customer_data["current_balance"] + order_amount) / customer_data["credit_limit"]) * 100, 2)
                }
            }
        })
        
    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error analyzing customer credit: {str(e)}"
        })


@tool
def payment_history_analyzer(customer_name: str, months_back: int = 12) -> str:
    """Analyze customer payment history and behavior patterns
    
    Args:
        customer_name: Name of the customer to analyze
        months_back: Number of months of history to analyze
        
    Returns:
        Payment behavior analysis with trends and predictions
    """
    try:
        # Mock payment history data
        payment_histories = {
            "ABC Manufacturing Corp": {
                "total_invoices": 24,
                "paid_on_time": 20,
                "paid_late": 4,
                "average_days_to_pay": 28,
                "longest_delay": 45,
                "disputes": 1,
                "payment_trend": "IMPROVING",
                "seasonal_patterns": ["Q4 slower payments"],
                "preferred_payment_method": "ACH",
                "early_payment_discounts_taken": 8,
                "payment_reliability_score": 85
            },
            "XYZ Corp": {
                "total_invoices": 18,
                "paid_on_time": 12,
                "paid_late": 6,
                "average_days_to_pay": 42,
                "longest_delay": 75,
                "disputes": 3,
                "payment_trend": "DECLINING",
                "seasonal_patterns": ["Irregular patterns"],
                "preferred_payment_method": "Check",
                "early_payment_discounts_taken": 2,
                "payment_reliability_score": 65
            }
        }
        
        history = payment_histories.get(customer_name)
        
        if not history:
            return json.dumps({
                "status": "NO_HISTORY",
                "customer_name": customer_name,
                "message": "No payment history available for this customer",
                "recommendation": "Require prepayment or credit insurance for new customer"
            })
        
        # Calculate payment performance metrics
        on_time_percentage = (history["paid_on_time"] / history["total_invoices"]) * 100
        late_payment_percentage = (history["paid_late"] / history["total_invoices"]) * 100
        
        # Determine payment risk level
        if on_time_percentage >= 90 and history["average_days_to_pay"] <= 35:
            payment_risk = "LOW"
        elif on_time_percentage >= 75 and history["average_days_to_pay"] <= 45:
            payment_risk = "MEDIUM"
        else:
            payment_risk = "HIGH"
        
        # Generate payment behavior insights
        insights = []
        if history["payment_trend"] == "IMPROVING":
            insights.append("Payment behavior is improving over time")
        elif history["payment_trend"] == "DECLINING":
            insights.append("Payment behavior is declining - requires attention")
        
        if history["disputes"] > 2:
            insights.append("High dispute frequency may indicate process issues")
        
        if history["early_payment_discounts_taken"] > 5:
            insights.append("Customer actively takes early payment discounts")
        
        return json.dumps({
            "status": "SUCCESS",
            "customer_name": customer_name,
            "analysis_period": f"Last {months_back} months",
            "payment_metrics": {
                "total_invoices": history["total_invoices"],
                "on_time_payments": history["paid_on_time"],
                "late_payments": history["paid_late"],
                "on_time_percentage": round(on_time_percentage, 2),
                "late_payment_percentage": round(late_payment_percentage, 2),
                "average_days_to_pay": history["average_days_to_pay"],
                "longest_payment_delay": history["longest_delay"],
                "payment_reliability_score": history["payment_reliability_score"]
            },
            "payment_behavior": {
                "payment_risk": payment_risk,
                "payment_trend": history["payment_trend"],
                "preferred_payment_method": history["preferred_payment_method"],
                "seasonal_patterns": history["seasonal_patterns"],
                "dispute_frequency": history["disputes"],
                "early_discount_usage": history["early_payment_discounts_taken"]
            },
            "insights": insights,
            "recommendations": [
                f"Payment terms: {'Standard' if payment_risk == 'LOW' else 'Restrictive' if payment_risk == 'HIGH' else 'Modified'}",
                f"Credit monitoring: {'Quarterly' if payment_risk == 'LOW' else 'Monthly' if payment_risk == 'MEDIUM' else 'Weekly'}",
                f"Collection priority: {'Low' if payment_risk == 'LOW' else 'High'}"
            ]
        })
        
    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error analyzing payment history: {str(e)}"
        })


@tool
def ar_aging_analyzer(customer_name: str) -> str:
    """Analyze accounts receivable aging and outstanding balances

    Args:
        customer_name: Name of the customer to analyze

    Returns:
        AR aging analysis with collection recommendations
    """
    try:
        # Mock AR aging data
        ar_data = {
            "ABC Manufacturing Corp": {
                "total_outstanding": 12500,
                "current": 8000,
                "days_1_30": 3000,
                "days_31_60": 1500,
                "days_61_90": 0,
                "days_over_90": 0,
                "oldest_invoice_days": 25,
                "dso": 28,
                "collection_efficiency": 95
            },
            "XYZ Corp": {
                "total_outstanding": 18000,
                "current": 5000,
                "days_1_30": 8000,
                "days_31_60": 3000,
                "days_61_90": 2000,
                "days_over_90": 0,
                "oldest_invoice_days": 75,
                "dso": 45,
                "collection_efficiency": 78
            }
        }

        ar_info = ar_data.get(customer_name, {
            "total_outstanding": 0,
            "current": 0,
            "days_1_30": 0,
            "days_31_60": 0,
            "days_61_90": 0,
            "days_over_90": 0,
            "oldest_invoice_days": 0,
            "dso": 0,
            "collection_efficiency": 100
        })

        # Calculate aging percentages
        total = ar_info["total_outstanding"]
        aging_percentages = {
            "current_pct": (ar_info["current"] / total * 100) if total > 0 else 0,
            "days_1_30_pct": (ar_info["days_1_30"] / total * 100) if total > 0 else 0,
            "days_31_60_pct": (ar_info["days_31_60"] / total * 100) if total > 0 else 0,
            "days_61_90_pct": (ar_info["days_61_90"] / total * 100) if total > 0 else 0,
            "days_over_90_pct": (ar_info["days_over_90"] / total * 100) if total > 0 else 0
        }

        # Determine collection risk
        if ar_info["days_over_90"] > 0:
            collection_risk = "HIGH"
        elif ar_info["days_61_90"] > total * 0.1:
            collection_risk = "MEDIUM"
        else:
            collection_risk = "LOW"

        return json.dumps({
            "status": "SUCCESS",
            "customer_name": customer_name,
            "ar_summary": {
                "total_outstanding": ar_info["total_outstanding"],
                "days_sales_outstanding": ar_info["dso"],
                "collection_efficiency": ar_info["collection_efficiency"],
                "oldest_invoice_days": ar_info["oldest_invoice_days"],
                "collection_risk": collection_risk
            },
            "aging_buckets": {
                "current": {"amount": ar_info["current"], "percentage": round(aging_percentages["current_pct"], 1)},
                "1_30_days": {"amount": ar_info["days_1_30"], "percentage": round(aging_percentages["days_1_30_pct"], 1)},
                "31_60_days": {"amount": ar_info["days_31_60"], "percentage": round(aging_percentages["days_31_60_pct"], 1)},
                "61_90_days": {"amount": ar_info["days_61_90"], "percentage": round(aging_percentages["days_61_90_pct"], 1)},
                "over_90_days": {"amount": ar_info["days_over_90"], "percentage": round(aging_percentages["days_over_90_pct"], 1)}
            },
            "recommendations": [
                "Immediate collection action required" if ar_info["days_over_90"] > 0 else "Standard collection procedures",
                "Credit hold recommended" if collection_risk == "HIGH" else "Normal credit terms acceptable",
                f"DSO target: {max(30, ar_info['dso'] - 5)} days"
            ]
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error analyzing AR aging: {str(e)}"
        })


@tool
def financial_risk_calculator(customer_name: str, order_amount: float, industry: str = "") -> str:
    """Calculate comprehensive financial risk score

    Args:
        customer_name: Name of the customer
        order_amount: Amount of the current order
        industry: Customer's industry sector

    Returns:
        Comprehensive risk score with detailed analysis
    """
    try:
        # Mock financial indicators
        financial_data = {
            "ABC Manufacturing Corp": {
                "debt_to_equity": 0.45,
                "current_ratio": 2.1,
                "quick_ratio": 1.8,
                "profit_margin": 8.5,
                "revenue_growth": 12.0,
                "market_cap": 50000000,
                "industry_rank": "Top 25%",
                "financial_stability": "Strong"
            },
            "XYZ Corp": {
                "debt_to_equity": 1.2,
                "current_ratio": 1.3,
                "quick_ratio": 0.9,
                "profit_margin": 3.2,
                "revenue_growth": -2.5,
                "market_cap": 8000000,
                "industry_rank": "Bottom 50%",
                "financial_stability": "Weak"
            }
        }

        fin_data = financial_data.get(customer_name, {
            "debt_to_equity": 1.0,
            "current_ratio": 1.5,
            "quick_ratio": 1.0,
            "profit_margin": 5.0,
            "revenue_growth": 0.0,
            "market_cap": 10000000,
            "industry_rank": "Average",
            "financial_stability": "Moderate"
        })

        # Calculate risk score components
        risk_score = 0
        risk_factors = []

        # Liquidity risk
        if fin_data["current_ratio"] < 1.2:
            risk_score += 25
            risk_factors.append("Poor liquidity position")
        elif fin_data["current_ratio"] < 1.5:
            risk_score += 15
            risk_factors.append("Moderate liquidity concerns")

        # Leverage risk
        if fin_data["debt_to_equity"] > 1.0:
            risk_score += 20
            risk_factors.append("High leverage")
        elif fin_data["debt_to_equity"] > 0.6:
            risk_score += 10
            risk_factors.append("Moderate leverage")

        # Profitability risk
        if fin_data["profit_margin"] < 3:
            risk_score += 20
            risk_factors.append("Low profitability")
        elif fin_data["profit_margin"] < 5:
            risk_score += 10
            risk_factors.append("Moderate profitability")

        # Growth risk
        if fin_data["revenue_growth"] < -5:
            risk_score += 25
            risk_factors.append("Declining revenue")
        elif fin_data["revenue_growth"] < 0:
            risk_score += 15
            risk_factors.append("Negative growth")

        # Order size risk
        annual_revenue = fin_data.get("annual_revenue", 10000000)
        order_percentage = (order_amount / annual_revenue) * 100
        if order_percentage > 5:
            risk_score += 15
            risk_factors.append("Large order relative to customer size")

        # Determine overall risk level
        if risk_score <= 20:
            risk_level = "LOW"
        elif risk_score <= 40:
            risk_level = "MEDIUM"
        elif risk_score <= 60:
            risk_level = "HIGH"
        else:
            risk_level = "VERY_HIGH"

        return json.dumps({
            "status": "SUCCESS",
            "customer_name": customer_name,
            "financial_metrics": {
                "debt_to_equity_ratio": fin_data["debt_to_equity"],
                "current_ratio": fin_data["current_ratio"],
                "quick_ratio": fin_data["quick_ratio"],
                "profit_margin": fin_data["profit_margin"],
                "revenue_growth": fin_data["revenue_growth"],
                "financial_stability": fin_data["financial_stability"]
            },
            "risk_assessment": {
                "overall_risk_score": risk_score,
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "order_analysis": {
                    "order_amount": order_amount,
                    "order_percentage_of_revenue": round(order_percentage, 2)
                }
            },
            "recommendations": [
                "Approve with standard terms" if risk_level == "LOW" else
                "Approve with enhanced monitoring" if risk_level == "MEDIUM" else
                "Require additional security" if risk_level == "HIGH" else
                "Recommend denial or prepayment"
            ]
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error calculating financial risk: {str(e)}"
        })


@tool
def credit_decision_engine(customer_name: str, order_amount: float, credit_score: int,
                          risk_level: str, payment_history_score: int, ar_risk: str) -> str:
    """Make final credit decision based on comprehensive analysis

    Args:
        customer_name: Name of the customer
        order_amount: Amount of the current order
        credit_score: Customer's credit score
        risk_level: Overall risk level (LOW/MEDIUM/HIGH/VERY_HIGH)
        payment_history_score: Payment reliability score (0-100)
        ar_risk: AR collection risk level

    Returns:
        Final credit decision with terms and conditions
    """
    try:
        # Credit decision matrix
        decision_matrix = {
            "LOW": {
                "credit_score_min": 700,
                "payment_score_min": 80,
                "max_order_amount": 100000,
                "decision": "APPROVE",
                "payment_terms": "Net 30",
                "monitoring": "Quarterly"
            },
            "MEDIUM": {
                "credit_score_min": 650,
                "payment_score_min": 70,
                "max_order_amount": 50000,
                "decision": "APPROVE_WITH_CONDITIONS",
                "payment_terms": "Net 15",
                "monitoring": "Monthly"
            },
            "HIGH": {
                "credit_score_min": 600,
                "payment_score_min": 60,
                "max_order_amount": 25000,
                "decision": "MANUAL_REVIEW",
                "payment_terms": "Net 10",
                "monitoring": "Weekly"
            },
            "VERY_HIGH": {
                "credit_score_min": 550,
                "payment_score_min": 50,
                "max_order_amount": 10000,
                "decision": "DENY",
                "payment_terms": "Prepayment Required",
                "monitoring": "Hold"
            }
        }

        criteria = decision_matrix.get(risk_level, decision_matrix["VERY_HIGH"])

        # Apply decision logic
        decision = criteria["decision"]
        conditions = []

        # Check credit score threshold
        if credit_score < criteria["credit_score_min"]:
            if decision == "APPROVE":
                decision = "APPROVE_WITH_CONDITIONS"
            elif decision == "APPROVE_WITH_CONDITIONS":
                decision = "MANUAL_REVIEW"
            conditions.append(f"Credit score below threshold ({credit_score} < {criteria['credit_score_min']})")

        # Check payment history threshold
        if payment_history_score < criteria["payment_score_min"]:
            if decision == "APPROVE":
                decision = "APPROVE_WITH_CONDITIONS"
            elif decision == "APPROVE_WITH_CONDITIONS":
                decision = "MANUAL_REVIEW"
            conditions.append(f"Payment history below threshold ({payment_history_score} < {criteria['payment_score_min']})")

        # Check order amount threshold
        if order_amount > criteria["max_order_amount"]:
            if decision in ["APPROVE", "APPROVE_WITH_CONDITIONS"]:
                decision = "MANUAL_REVIEW"
            conditions.append(f"Order amount exceeds limit (${order_amount:,.2f} > ${criteria['max_order_amount']:,.2f})")

        # Check AR risk
        if ar_risk == "HIGH":
            if decision == "APPROVE":
                decision = "APPROVE_WITH_CONDITIONS"
            conditions.append("High AR collection risk")

        # Generate specific terms based on decision
        if decision == "APPROVE":
            terms = {
                "payment_terms": "Net 30",
                "credit_insurance": "Not required",
                "personal_guarantee": "Not required",
                "monitoring_frequency": "Quarterly",
                "early_payment_discount": "2/10 Net 30"
            }
        elif decision == "APPROVE_WITH_CONDITIONS":
            terms = {
                "payment_terms": "Net 15",
                "credit_insurance": "Recommended",
                "personal_guarantee": "May be required",
                "monitoring_frequency": "Monthly",
                "early_payment_discount": "1/10 Net 15"
            }
        elif decision == "MANUAL_REVIEW":
            terms = {
                "payment_terms": "To be determined",
                "credit_insurance": "Required",
                "personal_guarantee": "Likely required",
                "monitoring_frequency": "Weekly",
                "early_payment_discount": "None"
            }
        else:  # DENY
            terms = {
                "payment_terms": "Prepayment only",
                "credit_insurance": "N/A",
                "personal_guarantee": "N/A",
                "monitoring_frequency": "N/A",
                "early_payment_discount": "None"
            }

        return json.dumps({
            "status": "SUCCESS",
            "customer_name": customer_name,
            "order_amount": order_amount,
            "credit_decision": {
                "decision": decision,
                "risk_level": risk_level,
                "conditions": conditions,
                "rationale": f"Based on credit score {credit_score}, payment history {payment_history_score}, and {risk_level} risk level"
            },
            "recommended_terms": terms,
            "next_steps": [
                "Process order immediately" if decision == "APPROVE" else
                "Apply special conditions and process" if decision == "APPROVE_WITH_CONDITIONS" else
                "Escalate to credit manager for review" if decision == "MANUAL_REVIEW" else
                "Decline order and offer alternative payment terms"
            ]
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error in credit decision engine: {str(e)}"
        })


@tool
def credit_limit_optimizer(customer_name: str, current_limit: float, order_amount: float,
                          performance_score: int, risk_level: str) -> str:
    """Optimize credit limits based on customer performance and risk

    Args:
        customer_name: Name of the customer
        current_limit: Current credit limit
        order_amount: Current order amount
        performance_score: Overall customer performance score
        risk_level: Current risk assessment

    Returns:
        Credit limit recommendations and adjustments
    """
    try:
        # Credit limit optimization logic
        recommendations = []
        new_limit = current_limit

        # Performance-based adjustments
        if performance_score >= 90 and risk_level == "LOW":
            # Excellent performance - increase limit
            increase_percentage = 25
            new_limit = current_limit * (1 + increase_percentage / 100)
            recommendations.append(f"Increase credit limit by {increase_percentage}% due to excellent performance")
        elif performance_score >= 80 and risk_level in ["LOW", "MEDIUM"]:
            # Good performance - moderate increase
            increase_percentage = 15
            new_limit = current_limit * (1 + increase_percentage / 100)
            recommendations.append(f"Increase credit limit by {increase_percentage}% due to good performance")
        elif performance_score < 60 or risk_level in ["HIGH", "VERY_HIGH"]:
            # Poor performance - decrease limit
            decrease_percentage = 20
            new_limit = current_limit * (1 - decrease_percentage / 100)
            recommendations.append(f"Decrease credit limit by {decrease_percentage}% due to poor performance/high risk")

        # Order-based adjustments
        if order_amount > current_limit:
            temp_increase = order_amount - current_limit + 5000  # Buffer
            recommendations.append(f"Temporary limit increase of ${temp_increase:,.2f} needed for current order")

        # Ensure minimum limits
        min_limit = 5000
        if new_limit < min_limit:
            new_limit = min_limit
            recommendations.append(f"Applied minimum credit limit of ${min_limit:,.2f}")

        # Calculate utilization impact
        current_utilization = 0  # Would get from AR data
        projected_utilization = (order_amount / new_limit) * 100

        return json.dumps({
            "status": "SUCCESS",
            "customer_name": customer_name,
            "current_credit_limit": current_limit,
            "recommended_credit_limit": round(new_limit, 2),
            "limit_change": round(new_limit - current_limit, 2),
            "limit_change_percentage": round(((new_limit - current_limit) / current_limit) * 100, 2),
            "utilization_analysis": {
                "current_utilization": current_utilization,
                "projected_utilization": round(projected_utilization, 2),
                "utilization_status": "Acceptable" if projected_utilization <= 75 else "High"
            },
            "recommendations": recommendations,
            "monitoring_requirements": [
                "Monthly limit review" if risk_level in ["HIGH", "VERY_HIGH"] else "Quarterly limit review",
                "Automatic limit reduction trigger at 90% utilization",
                "Performance review every 6 months"
            ]
        })

    except Exception as e:
        return json.dumps({
            "status": "ERROR",
            "message": f"Error optimizing credit limit: {str(e)}"
        })


def get_credit_management_tools():
    """Get all credit management tools for the agent"""
    return [
        customer_credit_analyzer,
        payment_history_analyzer,
        ar_aging_analyzer,
        financial_risk_calculator,
        credit_decision_engine,
        credit_limit_optimizer
    ]
