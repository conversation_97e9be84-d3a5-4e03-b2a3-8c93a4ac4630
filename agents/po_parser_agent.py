#!/usr/bin/env python3
"""
PO Parser Agent for O2C System
CrewAI agent that parses PO text files using DeepSeek LLM
"""

import os
import sys
from crewai import Agent, Task, Crew
from agents.llm.deepseek_llm import create_deepseek_llm_for_parsing
from agents.tools.po_parsing_tools import get_po_parsing_tools
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class POParserAgent:
    """PO Parser Agent using CrewAI and DeepSeek LLM"""
    
    def __init__(self, deepseek_api_key: str = None):
        """Initialize the PO Parser Agent"""
        # Set API key if provided
        if deepseek_api_key:
            os.environ["DEEPSEEK_API_KEY"] = deepseek_api_key

        self.llm = create_deepseek_llm_for_parsing()
        self.tools = get_po_parsing_tools()
        self.agent = self._create_agent()
    
    def _create_agent(self) -> Agent:
        """Create the CrewAI PO Parser Agent"""
        return Agent(
            role="Purchase Order Parser Specialist",
            goal="Extract and structure purchase order data from text files with high accuracy",
            backstory="""You are an expert in parsing purchase order documents. You have extensive 
            experience in extracting structured data from various PO formats including email content 
            and text attachments. You understand business terminology, product codes, pricing structures, 
            and delivery terms. Your expertise ensures that no critical information is missed and all 
            data is accurately categorized.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm,
            tools=self.tools
        )
    
    def create_parsing_task(self, po_filename: str = None) -> Task:
        """Create a task to parse a specific PO file"""
        
        if po_filename:
            task_description = f"""
            Parse the purchase order file '{po_filename}' and extract all relevant information into a structured JSON format.
            
            Follow these steps:
            1. Use the po_file_reader tool to read the content of '{po_filename}'
            2. Analyze the content and extract the following information:
               - PO number and date
               - Vendor information (name, address, contact details)
               - Customer/buyer information (name, address, contact details)
               - Line items with product codes, descriptions, quantities, unit prices, and totals
               - Pricing summary (subtotal, tax, shipping, total amount)
               - Delivery information (address, requested date, special instructions)
               - Payment terms and conditions
            
            3. Structure the data in this JSON format:
            {{
                "po_number": "string",
                "date": "YYYY-MM-DD",
                "vendor": {{
                    "name": "string",
                    "address": "string",
                    "phone": "string",
                    "email": "string"
                }},
                "customer": {{
                    "name": "string",
                    "address": "string", 
                    "phone": "string",
                    "email": "string"
                }},
                "line_items": [
                    {{
                        "item_code": "string",
                        "description": "string",
                        "quantity": number,
                        "unit_price": number,
                        "line_total": number
                    }}
                ],
                "pricing": {{
                    "subtotal": number,
                    "tax": number,
                    "shipping": number,
                    "total": number
                }},
                "delivery": {{
                    "address": "string",
                    "requested_date": "YYYY-MM-DD",
                    "special_instructions": "string"
                }},
                "terms": {{
                    "payment_terms": "string",
                    "conditions": "string"
                }}
            }}
            
            4. Use the po_validator tool to validate the extracted data
            5. Use the po_data_saver tool to save the structured data as JSON
            
            Ensure all numerical values are properly parsed and all dates are in YYYY-MM-DD format.
            """
        else:
            task_description = """
            List all available PO files and parse the most recent one.
            
            Follow these steps:
            1. Use the po_list tool to see available PO files
            2. Select the most recent PO file
            3. Parse it following the same structured extraction process as described above
            4. Validate and save the results
            """
        
        return Task(
            description=task_description,
            agent=self.agent,
            expected_output="Structured JSON data containing all PO information with validation confirmation and file save location"
        )
    
    def parse_po_file(self, po_filename: str = None) -> str:
        """Parse a PO file using the CrewAI agent"""
        try:
            # Create task
            task = self.create_parsing_task(po_filename)
            
            # Create crew with single agent
            crew = Crew(
                agents=[self.agent],
                tasks=[task],
                verbose=True
            )
            
            # Execute the task
            result = crew.kickoff()
            
            return str(result)
            
        except Exception as e:
            return f"Error parsing PO file: {str(e)}"
    
    def parse_all_pending_pos(self) -> str:
        """Parse all pending PO files in the incoming folder"""
        try:
            # Get list of files
            list_tool = self.tools[3]  # POListTool
            files_list = list_tool._run()
            
            if "No PO files found" in files_list:
                return "No pending PO files to process"
            
            # Extract filenames from the list
            lines = files_list.split('\n')
            filenames = []
            for line in lines:
                if line.startswith('- ') and '.txt' in line:
                    filename = line.split(' ')[1]
                    filenames.append(filename)
            
            results = []
            for filename in filenames:
                print(f"\nProcessing {filename}...")
                result = self.parse_po_file(filename)
                results.append(f"File: {filename}\nResult: {result}\n")
            
            return "\n".join(results)
            
        except Exception as e:
            return f"Error parsing multiple PO files: {str(e)}"


def main():
    """Main function for testing the PO Parser Agent"""
    print("=== PO Parser Agent Test ===\n")
    
    # Check for DeepSeek API key
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("Error: DEEPSEEK_API_KEY environment variable not set")
        print("Please set your DeepSeek API key:")
        print("export DEEPSEEK_API_KEY='your-api-key-here'")
        return
    
    try:
        # Create PO Parser Agent
        parser = POParserAgent(deepseek_api_key=api_key)
        
        # Check command line arguments
        if len(sys.argv) > 1:
            filename = sys.argv[1]
            print(f"Parsing specific file: {filename}")
            result = parser.parse_po_file(filename)
        else:
            print("Parsing most recent PO file...")
            result = parser.parse_po_file()
        
        print("\n=== Parsing Result ===")
        print(result)
        
    except Exception as e:
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
