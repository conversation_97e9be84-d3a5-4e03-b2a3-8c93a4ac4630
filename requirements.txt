# Core dependencies for O2C Email Monitoring Service
# Python 3.8+ required

# Email processing
imaplib2==3.6
email-validator==2.0.0

# Data processing and validation
pydantic==2.5.0
python-dateutil==2.8.2

# Configuration and logging
python-json-logger==2.0.7

# Testing
pytest==7.4.3
pytest-mock==3.12.0

# CrewAI and LLM dependencies
crewai==0.70.1
langchain==0.3.7
langchain-openai==0.2.8
openai==1.54.4

# DeepSeek integration
requests==2.32.3

# Additional dependencies for structured output
pydantic==2.5.0

# Environment variables
python-dotenv==1.0.0
