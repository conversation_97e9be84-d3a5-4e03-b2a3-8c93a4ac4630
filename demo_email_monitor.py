#!/usr/bin/env python3
"""
Demo script for O2C Email Monitoring Service
Simulates email monitoring and PO extraction without requiring Gmail setup
"""

import os
import json
from datetime import datetime
from services.email_monitor import EmailMonitor


def create_sample_emails():
    """Create sample email data for demonstration"""
    sample_emails = [
        {
            "email_id": "001",
            "sender": "<EMAIL>",
            "subject": "Purchase Order PO-2024-001",
            "date": "Mon, 8 Jul 2024 10:30:00 +0000",
            "body": """
Dear Vendor,

Please find our purchase order details below:

Purchase Order Number: PO-2024-001
Order Date: July 8, 2024
Delivery Date: July 15, 2024

Ship To:
ABC Company Inc.
123 Main Street
Anytown, CA 12345

Items Ordered:
1. Product Code: WIDGET-001
   Description: Premium Widget Type A
   Quantity: 100
   Unit Price: $25.00
   Total: $2,500.00

2. Product Code: WIDGET-002
   Description: Standard Widget Type B
   Quantity: 50
   Unit Price: $35.00
   Total: $1,750.00

Subtotal: $4,250.00
Tax (8.25%): $350.63
Total Amount: $4,600.63

Payment Terms: Net 30
Special Instructions: Please deliver to loading dock B

Best regards,
<PERSON>
Procurement Manager
""",
            "attachments": ["PO-2024-001.pdf"],
            "text_attachments": {
                "PO-2024-001.txt": """PURCHASE ORDER DOCUMENT

PO Number: PO-2024-001
Date: July 8, 2024
Vendor: ABC Company Inc.

LINE ITEMS:
1. WIDGET-001 | Premium Widget Type A | Qty: 100 | $25.00 | $2,500.00
2. WIDGET-002 | Standard Widget Type B | Qty: 50 | $35.00 | $1,750.00

SUBTOTAL: $4,250.00
TAX: $350.63
TOTAL: $4,600.63

DELIVERY: July 15, 2024
TERMS: Net 30
"""
            }
        },
        {
            "email_id": "002",
            "sender": "<EMAIL>",
            "subject": "Urgent P.O. Request - REQ-789",
            "date": "Mon, 8 Jul 2024 14:15:00 +0000",
            "body": """
URGENT PURCHASE ORDER

P.O. Number: REQ-789
Requested By: Sarah Johnson
Department: IT Operations

Items Required:
- Laptop Computers (Model: XYZ-123) - Qty: 25 - $1,200 each
- Wireless Mice - Qty: 25 - $45 each
- USB Cables - Qty: 50 - $15 each

Total Estimated Value: $31,875.00

Delivery Required By: July 12, 2024
Ship To: IT Department, Building C

Please confirm receipt and provide delivery timeline.

Contact: <EMAIL>
Phone: ************
""",
            "attachments": [],
            "text_attachments": {}
        },
        {
            "email_id": "003",
            "sender": "<EMAIL>",
            "subject": "Monthly Office Supplies Order",
            "date": "Mon, 8 Jul 2024 16:45:00 +0000",
            "body": """
Hi there,

This is our regular monthly office supplies order:

Order Number: OFF-2024-07
Date: July 8, 2024

Items:
- Copy Paper (500 sheets/pack) - 20 packs - $8.99 each
- Blue Pens - 10 boxes - $12.50 each
- Sticky Notes - 15 pads - $3.25 each
- Staples - 5 boxes - $4.75 each

Subtotal: $382.55
Shipping: $25.00
Total: $407.55

Please process and ship to our usual address.

Thanks!
Mike Chen
Office Manager
""",
            "attachments": [],
            "text_attachments": {}
        }
    ]
    
    return sample_emails


def simulate_po_extraction(sample_emails):
    """Simulate the PO extraction process"""
    print("=== O2C Email Monitor Demo ===\n")
    print("Simulating email monitoring and PO extraction...\n")
    
    # Create a demo email monitor (without Gmail connection)
    monitor = EmailMonitor()
    
    # Create output directory
    os.makedirs("demo_output", exist_ok=True)
    
    extracted_files = []
    
    for i, email_data in enumerate(sample_emails, 1):
        print(f"Processing Email {i}/{len(sample_emails)}:")
        print(f"  From: {email_data['sender']}")
        print(f"  Subject: {email_data['subject']}")
        
        # Simulate PO detection
        subject_keywords = monitor.config["po_detection"]["subject_keywords"]
        body_keywords = monitor.config["po_detection"]["body_keywords"]
        
        subject_lower = email_data['subject'].lower()
        body_lower = email_data['body'].lower()
        
        # Check if this email would be detected as a PO
        subject_match = any(keyword.lower() in subject_lower for keyword in subject_keywords)
        body_match = any(keyword.lower() in body_lower for keyword in body_keywords)

        # Check text attachments for PO keywords
        attachment_match = False
        if email_data.get('text_attachments'):
            for filename, content in email_data['text_attachments'].items():
                content_lower = content.lower()
                if any(keyword.lower() in content_lower for keyword in body_keywords):
                    attachment_match = True
                    break
        
        if subject_match or body_match or attachment_match:
            print("  ✓ Detected as PO email")
            
            # Create PO data structure
            po_data = {
                "email_id": email_data["email_id"],
                "timestamp": datetime.now().isoformat(),
                "sender": email_data["sender"],
                "subject": email_data["subject"],
                "date": email_data["date"],
                "body": email_data["body"],
                "attachments": email_data["attachments"],
                "text_attachments": email_data.get("text_attachments", {})
            }
            
            # Save to demo output
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"PO_DEMO_{timestamp}_{email_data['email_id']}.txt"
            filepath = os.path.join("demo_output", filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"=== EMAIL METADATA ===\n")
                f.write(f"Email ID: {po_data['email_id']}\n")
                f.write(f"Timestamp: {po_data['timestamp']}\n")
                f.write(f"Sender: {po_data['sender']}\n")
                f.write(f"Subject: {po_data['subject']}\n")
                f.write(f"Date: {po_data['date']}\n")
                f.write(f"Attachments: {', '.join(po_data['attachments'])}\n")
                f.write(f"\n=== EMAIL BODY ===\n")
                f.write(po_data['body'])

                # Add text attachment content if any
                if po_data.get('text_attachments'):
                    f.write(f"\n\n=== TEXT ATTACHMENTS ===\n")
                    for filename, content in po_data['text_attachments'].items():
                        f.write(f"\n--- ATTACHMENT: {filename} ---\n")
                        f.write(content)
                        f.write(f"\n--- END OF {filename} ---\n")
            
            extracted_files.append(filepath)
            print(f"  ✓ Extracted to: {filename}")
        else:
            print("  ✗ Not detected as PO email")
        
        print()
    
    return extracted_files


def show_extracted_content(extracted_files):
    """Display the content of extracted PO files"""
    print("=== Extracted PO Content ===\n")
    
    for i, filepath in enumerate(extracted_files, 1):
        print(f"File {i}: {os.path.basename(filepath)}")
        print("-" * 50)
        
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            # Show first 500 characters
            if len(content) > 500:
                print(content[:500] + "...")
            else:
                print(content)
        
        print("-" * 50)
        print()


def show_detection_stats(sample_emails):
    """Show detection statistics"""
    monitor = EmailMonitor()
    
    total_emails = len(sample_emails)
    detected_pos = 0
    
    for email_data in sample_emails:
        subject_keywords = monitor.config["po_detection"]["subject_keywords"]
        body_keywords = monitor.config["po_detection"]["body_keywords"]
        
        subject_lower = email_data['subject'].lower()
        body_lower = email_data['body'].lower()
        
        subject_match = any(keyword.lower() in subject_lower for keyword in subject_keywords)
        body_match = any(keyword.lower() in body_lower for keyword in body_keywords)
        
        if subject_match or body_match:
            detected_pos += 1
    
    print("=== Detection Statistics ===")
    print(f"Total Emails Processed: {total_emails}")
    print(f"PO Emails Detected: {detected_pos}")
    print(f"Detection Rate: {(detected_pos/total_emails)*100:.1f}%")
    print(f"Output Directory: demo_output/")
    print()


def main():
    """Main demo function"""
    print("O2C Email Monitoring Service - Demo Mode\n")
    
    # Create sample emails
    sample_emails = create_sample_emails()
    
    # Simulate PO extraction
    extracted_files = simulate_po_extraction(sample_emails)
    
    # Show statistics
    show_detection_stats(sample_emails)
    
    # Show extracted content
    if extracted_files:
        show_extracted_content(extracted_files)
    
    print("=== Demo Complete ===")
    print("Next Steps:")
    print("1. Configure Gmail credentials: python3 run_email_monitor.py --setup")
    print("2. Run real email monitoring: python3 run_email_monitor.py --run")
    print("3. Check extracted POs in: data/incoming_pos/")


if __name__ == "__main__":
    main()
